#version 450 core

layout (location = 0) in vec3 position;
layout (location = 1) in vec3 normal;
layout (location = 2) in vec3 tangent;
layout (location = 3) in vec2 texcoord;
layout (location = 4) in vec4 color;

out VS_OUT {
    vec3 FragPos;
    vec3 Normal;
    vec2 TexCoords;
    vec4 FragPosLightSpace;
    vec3 fragNormal;
    vec3 fragTangent;
    vec4 fragColor;
} vs_out;

uniform mat4 model;
uniform mat4 view;
uniform mat4 projection;
uniform mat4 lightSpaceMatrix;

void main()
{
    vec4 worldPosition = model * vec4(position, 1.0);
    vs_out.FragPos = vec3(worldPosition);
    vs_out.Normal = transpose(inverse(mat3(model))) * normal;
    vs_out.TexCoords = texcoord;
    vs_out.FragPosLightSpace = lightSpaceMatrix * model * vec4(position, 1.0); // Apply model matrix to light space transformation
    vs_out.fragNormal = mat3(transpose(inverse(model))) * normal; // Transform normal to world space
    vs_out.fragTangent = mat3(transpose(inverse(model))) * tangent; // Transform tangent to world space
    vs_out.fragColor = color; // Assign the input color to fragColor

    gl_Position = projection * view * worldPosition;
}