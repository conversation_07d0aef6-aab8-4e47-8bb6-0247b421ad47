#include <glen.h>
#include <filesystem>
#include <windows.h>
#include <imgui.h>
#include <imgui_internal.h>
#include <imgui_impl_glfw.h>
#include <imgui_impl_opengl3.h>

#include "Editor/Editor.h"
#include "Editor/EditorGizmos.h"
#include "GUI/UI_SceneHierarchy.h"
#include "GUI/UI_Inspector.h"

int sceneWidth = 1280;
int sceneHeight = 720;

void ImGuiNodeEditorWindow()
{
    ImGui::Begin("Node Editor");

    // Node positions (persistent)
    static ImVec2 nodeA_pos = ImVec2(100, 100);
    static ImVec2 nodeB_pos = ImVec2(400, 250);
    ImVec2 node_size = ImVec2(120, 60);

    // Get window position for correct node placement
    ImVec2 window_pos = ImGui::GetWindowPos();
    ImVec2 window_scroll(ImGui::GetScrollX(), ImGui::GetScrollY());

    // Helper lambda for node drag
    auto DragNode = [](const char *label, ImVec2 &node_pos, ImVec2 node_size, ImVec2 window_pos, ImVec2 window_scroll)
    {
        ImGui::SetCursorScreenPos(ImVec2(window_pos.x + node_pos.x - window_scroll.x, window_pos.y + node_pos.y - window_scroll.y));
        ImGui::BeginGroup();
        ImGui::Button(label, node_size);
        // Drag logic
        ImVec2 cursor = ImGui::GetIO().MousePos;
        ImVec2 rel = ImVec2(
            cursor.x - window_pos.x + window_scroll.x,
            cursor.y - window_pos.y + window_scroll.y);
        if (ImGui::IsItemActive() && ImGui::IsMouseDragging(ImGuiMouseButton_Left))
        {
            node_pos.x += ImGui::GetIO().MouseDelta.x;
            node_pos.y += ImGui::GetIO().MouseDelta.y;
        }
        ImGui::EndGroup();
        // Return center of right/left edge for bezier
        ImVec2 center = ImVec2(node_pos.x + node_size.x * 0.5f, node_pos.y + node_size.y * 0.5f);
        ImVec2 right = ImVec2(node_pos.x + node_size.x, node_pos.y + node_size.y * 0.5f);
        ImVec2 left = ImVec2(node_pos.x, node_pos.y + node_size.y * 0.5f);
        return std::make_pair(left, right);
    };

    // Draw nodes and get their connection points
    auto nodeA_edges = DragNode("Node A", nodeA_pos, node_size, window_pos, window_scroll);
    auto nodeB_edges = DragNode("Node B", nodeB_pos, node_size, window_pos, window_scroll);

    // Draw bezier curve between nodes (screen space)
    ImDrawList *draw_list = ImGui::GetWindowDrawList();
    ImVec2 p1 = ImVec2(window_pos.x + nodeA_edges.second.x - window_scroll.x, window_pos.y + nodeA_edges.second.y - window_scroll.y); // Right middle of Node A
    ImVec2 p2 = ImVec2(window_pos.x + nodeB_edges.first.x - window_scroll.x, window_pos.y + nodeB_edges.first.y - window_scroll.y);   // Left middle of Node B
    ImVec2 cp1 = ImVec2(p1.x + 80, p1.y);
    ImVec2 cp2 = ImVec2(p2.x - 80, p2.y);

    draw_list->AddBezierCubic(
        p1, cp1, cp2, p2,
        IM_COL32(200, 200, 100, 255), 3.0f);

        

    ImGui::End();
}

int AddItems(Scene *scene)
{

    auto gameObject = std::make_shared<GameObject>("MainObject");

    auto mesh = std::make_shared<Mesh>("resources/models/meshes.bin");

    // loop through the submeshes and set the material
    for (auto &subMesh : mesh->subMeshes)
    {
        // std::string pathToTexture = "C:\\Program Files (x86)\\Aliens vs. Predator 2\\AVP2\\WorldTextures\\" + subMesh.texture;
        std::string pathToTexture = "resources\\textures\\" + subMesh.texture;
        // auto texture = DTX::LoadDTX(pathToTexture);
        // Renderer::GetTextureManager()->AddTexture(subMesh.texture, texture);
        Renderer::GetTextureManager()->LoadTexture(pathToTexture);
    }

    Renderer::GetTextureManager()->LoadTexture("resources\\textures\\normal.dtx");

    auto meshRenderer = std::make_shared<MeshRenderer>(mesh);
    gameObject->AddComponent(meshRenderer);
    gameObject->GetComponent<Transform>()->SetPosition(glm::vec3(0.0f, 0.0f, 0.0f));
    gameObject->GetComponent<Transform>()->Rotate(glm::vec3(90.0f, 180.0f, 180.0f));
    gameObject->GetComponent<Transform>()->SetScale(glm::vec3(0.5f));
    AddGameObject(gameObject);

    auto test = std::make_shared<GameObject>("TestObject");
    std::shared_ptr<Camera> camera = std::make_shared<Camera>();
    camera->fov = 45.0f;
    test->AddComponent(camera);

    AddGameObject(test);

    // Create a directional light for VSM shadows
    auto directionalLight = std::make_shared<GameObject>("DirectionalLight");
    directionalLight->AddComponent(std::make_shared<Light>());
    AddGameObject(directionalLight);

    // Configure the directional light
    directionalLight->GetComponent<Light>()->SetColor(glm::vec3(1.0f, 1.0f, 0.9f));
    directionalLight->GetComponent<Light>()->m_Type = Light::LightType::DIRECTIONAL;
    directionalLight->GetComponent<Light>()->m_fIntensity = 1.0f; // Higher intensity for directional light (sun-like)
    directionalLight->GetComponent<Light>()->m_bShadowEnabled = true;
    directionalLight->GetComponent<Light>()->m_bSoftShadows = true; // Enable soft shadows
    directionalLight->GetComponent<Light>()->m_iShadowBlurIterations = 10; // High quality blur

    // Position and orient the directional light (simulating sun from above and slightly angled)
    directionalLight->GetComponent<Transform>()->SetPosition(glm::vec3(8.0f, 30.0f, 12.0f));
    directionalLight->GetComponent<Transform>()->SetRotation(glm::vec3(glm::radians(-60.0f), glm::radians(30.0f), 0.0f));

    // make 3 new game objects with point lights
    auto light1 = std::make_shared<GameObject>("Light1");
    auto light2 = std::make_shared<GameObject>("Light2");
    auto light3 = std::make_shared<GameObject>("Light3");

    light1->AddComponent(std::make_shared<Light>());
    light2->AddComponent(std::make_shared<Light>());
    light3->AddComponent(std::make_shared<Light>());

    AddGameObject(light1);
    AddGameObject(light2);
    AddGameObject(light3);

    light1->GetComponent<Light>()->SetColor(glm::vec3(1.0f, 0.8f, 0.9f));
    light1->GetComponent<Light>()->m_Type = Light::LightType::POINT;
    light1->GetComponent<Light>()->m_fRange = 10.0f;
    light1->GetComponent<Light>()->m_bShadowEnabled = true; // Enable shadows to test smoothness
    light1->GetComponent<Light>()->m_bSoftShadows = true; // Enable soft shadows
    light1->GetComponent<Light>()->SetShadowMapSize(512.0f); // High resolution for smooth shadows
    light1->GetComponent<Light>()->m_iShadowBlurIterations = 6; // Moderate blur for point lights
    light1->GetComponent<Transform>()->SetPosition(glm::vec3(2.0f, 3.0f, 2.0f)); // Position for good shadow visibility

    light2->GetComponent<Light>()->SetColor(glm::vec3(0.8f, 0.6f, 0.9f));
    light2->GetComponent<Light>()->m_Type = Light::LightType::POINT;
    light2->GetComponent<Light>()->m_fRange = 10.0f;
    light2->GetComponent<Light>()->m_bShadowEnabled = true; // Enable shadows for comparison
    light2->GetComponent<Light>()->m_bSoftShadows = false; // Disable soft shadows for comparison
    light2->GetComponent<Light>()->SetShadowMapSize(512.0f); // Standard resolution
    light2->GetComponent<Light>()->m_iShadowBlurIterations = 4; // Light blur (won't be used due to soft shadows disabled)
    light2->GetComponent<Transform>()->SetPosition(glm::vec3(-2.0f, 3.0f, -2.0f));

    light3->GetComponent<Light>()->SetColor(glm::vec3(0.6f, 0.8f, 0.9f));
    light3->GetComponent<Light>()->m_Type = Light::LightType::POINT;
    light3->GetComponent<Light>()->m_fRange = 10.0f;
    light3->GetComponent<Light>()->m_bShadowEnabled = false; // Keep one without shadows for performance comparison
    light3->GetComponent<Transform>()->SetPosition(glm::vec3(0.0f, 5.0f, 0.0f));

    Renderer::SetAmbientLight(glm::vec3(0.1f, 0.1f, 0.1f));

    SoundManager::GetInstance().LoadAudioFile("test", "resources/sounds/FIRE_SONG.WAV");
    SoundManager::GetInstance().LoadAudioFile("chainlift", "resources/sounds/chainlift.wav");

    auto soundObject = std::make_shared<Sound>("chainlift");
    soundObject->Set3D(true);
    soundObject->SetLoop(true);
    soundObject->SetVolume(25.0f);
    light3->AddComponent(soundObject);

    // auto soundObject2 = std::make_shared<Sound>("test");
    // soundObject2->Set3D(false);
    // soundObject2->SetLoop(true);
    // soundObject2->SetVolume(0.0f);
    // light2->AddComponent(soundObject2);

    auto child = std::make_shared<GameObject>("ChildObject");
    light3->AddChild(child);

    auto child2 = std::make_shared<GameObject>("ChildObject2");
    child->AddChild(child2);

    HMODULE lib = LoadLibraryA("glen-game.dll");
    if (!lib)
    {
        std::cerr << "Failed to load DLL\n";
        return 1;
    }

    using CreateFunc = IComponent *(*)(const char *);
    CreateFunc CreateComponent = (CreateFunc)GetProcAddress(lib, "CreateComponent");

    using SetOpenGLContextFunc = void (*)(HGLRC, HDC);
    SetOpenGLContextFunc SetOpenGLContext = (SetOpenGLContextFunc)GetProcAddress(lib, "SetOpenGLContext");

    HGLRC hglrc = wglGetCurrentContext();
    if (!hglrc)
    {
        std::cerr << "Failed to get current OpenGL context\n";
        return 1;
    }
    // hdc
    HDC hdc = wglGetCurrentDC();
    if (!hdc)
    {
        std::cerr << "Failed to get current OpenGL device context\n";
        return 1;
    }

    SetOpenGLContext(hglrc, hdc);

    IComponent *comp = CreateComponent("MyComponent");
    comp->SetInput(&InputManager::GetInstance());
    comp->SetTime(g_Time);
    comp->SetScene(scene);
    auto compShared = std::shared_ptr<IComponent>(comp);
    // test->AddComponent(std::dynamic_pointer_cast<Component>(compShared));

    IComponent *comp2 = CreateComponent("MyComponent2");
    comp2->SetInput(&InputManager::GetInstance());
    comp2->SetTime(g_Time);
    comp2->SetScene(scene);
    auto comp2Shared = std::shared_ptr<IComponent>(comp2);
    // test->AddComponent(std::dynamic_pointer_cast<Component>(comp2Shared));

    meshRenderer->BuildMaterialList();

    return 1;
}

void RenderIMGUI()
{
    ImGui_ImplOpenGL3_NewFrame();
    ImGui_ImplGlfw_NewFrame();
    ImGui::NewFrame();

    // Setup main dockspace window
    ImGuiWindowFlags window_flags = ImGuiWindowFlags_MenuBar | ImGuiWindowFlags_NoDocking;
    const ImGuiViewport *viewport = ImGui::GetMainViewport();
    ImGui::SetNextWindowPos(viewport->WorkPos);
    ImGui::SetNextWindowSize(viewport->WorkSize);
    ImGui::SetNextWindowViewport(viewport->ID);
    window_flags |= ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoCollapse | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoMove;
    window_flags |= ImGuiWindowFlags_NoBringToFrontOnFocus | ImGuiWindowFlags_NoNavFocus;

    ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 0.0f);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 0.0f);
    ImGui::SetNextWindowBgAlpha(0.0f); // Make dockspace window see-through

    ImGui::Begin("DockSpace Demo", nullptr, window_flags);

    // Top menu bar
    if (ImGui::BeginMenuBar())
    {
        if (ImGui::BeginMenu("File"))
        {
            ImGui::MenuItem("New", "Ctrl+N");
            ImGui::MenuItem("Open...", "Ctrl+O");
            ImGui::MenuItem("Save", "Ctrl+S");
            ImGui::Separator();
            ImGui::MenuItem("Exit");
            ImGui::EndMenu();
        }
        if (ImGui::BeginMenu("Edit"))
        {
            ImGui::MenuItem("Undo", "Ctrl+Z");
            ImGui::MenuItem("Redo", "Ctrl+Y");
            ImGui::Separator();
            ImGui::MenuItem("Cut", "Ctrl+X");
            ImGui::MenuItem("Copy", "Ctrl+C");
            ImGui::MenuItem("Paste", "Ctrl+V");
            ImGui::EndMenu();
        }
        if (ImGui::BeginMenu("View"))
        {
            ImGui::MenuItem("Show Toolbar");
            ImGui::MenuItem("Show Status Bar");
            ImGui::EndMenu();
        }
        if (ImGui::BeginMenu("Help"))
        {
            ImGui::MenuItem("About");
            ImGui::EndMenu();
        }
        ImGui::EndMenuBar();
    }

    ImGui::PopStyleVar(2);

    // DockSpace
    ImGuiID dockspace_id = ImGui::GetID("MyDockSpace");
    ImGui::DockSpace(dockspace_id, ImVec2(0.0f, 0.0f), ImGuiDockNodeFlags_None);

    // --- Unity-like Editor Layout ---
    static bool initialized = false;
    if (!initialized)
    {
        initialized = true;
        ImGui::DockBuilderRemoveNode(dockspace_id); // clear any previous layout
        ImGui::DockBuilderAddNode(dockspace_id, ImGuiDockNodeFlags_DockSpace);
        ImGui::DockBuilderSetNodeSize(dockspace_id, viewport->Size);

        // Split the dockspace into 5 nodes: left, right, bottom, center, top
        ImGuiID dock_main_id = dockspace_id;
        ImGuiID dock_left_id = ImGui::DockBuilderSplitNode(dock_main_id, ImGuiDir_Left, 0.18f, nullptr, &dock_main_id);
        ImGuiID dock_right_id = ImGui::DockBuilderSplitNode(dock_main_id, ImGuiDir_Right, 0.22f, nullptr, &dock_main_id);
        ImGuiID dock_bottom_id = ImGui::DockBuilderSplitNode(dock_main_id, ImGuiDir_Down, 0.25f, nullptr, &dock_main_id);
        ImGuiID dock_top_id = ImGui::DockBuilderSplitNode(dock_main_id, ImGuiDir_Up, 0.08f, nullptr, &dock_main_id);

        // Dock windows
        ImGui::DockBuilderDockWindow("Scene Hierarchy", dock_left_id);
        ImGui::DockBuilderDockWindow("Inspector", dock_right_id);
        ImGui::DockBuilderDockWindow("Console", dock_bottom_id);
        ImGui::DockBuilderDockWindow("Toolbar", dock_top_id);
        ImGui::DockBuilderDockWindow("Game", dock_main_id);
        ImGui::DockBuilderDockWindow("Scene", dock_main_id);

        ImGui::DockBuilderFinish(dockspace_id);
    }

    // Toolbar (top)
    ImGui::Begin("Toolbar", nullptr, ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoMove | ImGuiWindowFlags_NoScrollbar | ImGuiWindowFlags_NoScrollWithMouse | ImGuiWindowFlags_NoCollapse);
    // play button
    if (ImGui::Button("Play"))
    {
        // Handle play button click
    }
    ImGui::SameLine();
    // pause button
    if (ImGui::Button("Pause"))
    {
        // Handle pause button click
    }
    ImGui::SameLine();
    // stop button
    if (ImGui::Button("Stop"))
    {
        // Handle stop button click
    }

    ImGui::SameLine();
    ImGui::Text("FPS: %.1f", ImGui::GetIO().Framerate);
    ImGui::SameLine();
    ImGui::Text("Active Scene: %s", Scene::GetActiveScene()->GetName().c_str());
    ImGui::SameLine();
    ImGui::End();

    // Hierarchy (left)
    RenderSceneHierarchyWindow();

    RenderInspectorWindow();

    // Console (bottom)
    ImGui::Begin("Console");
    ImGui::Text("Console Output...");

    // show hovered or focused scene
    if (Editor::EditorState::GetInstance().bIsSceneEditorHovered)
    {
        ImGui::Text("Scene Editor: Hovered");
    }
    else
    {
        ImGui::Text("Scene Editor: Not Hovered");
    }
    if (Editor::EditorState::GetInstance().bIsSceneEditorFocused)
    {
        ImGui::Text("Scene Editor: Focused");
    }
    else
    {
        ImGui::Text("Scene Editor: Not Focused");
    }
    
    ImGui::End();

    // Game (center, tabbed with Scene)
    ImGui::Begin("Game");
    ImGui::Text("Game View");
    ImGui::End();

    // Scene (center)
    ImGui::Begin("Scene");
    // Get the visible content region size
    ImVec2 sceneContentMin = ImGui::GetWindowContentRegionMin();
    ImVec2 sceneContentMax = ImGui::GetWindowContentRegionMax();
    ImVec2 sceneWindowPos = ImGui::GetWindowPos();
    ImVec2 sceneContentPos = ImVec2(sceneWindowPos.x + sceneContentMin.x, sceneWindowPos.y + sceneContentMin.y);
    ImVec2 sceneContentSize = ImVec2(sceneContentMax.x - sceneContentMin.x, sceneContentMax.y - sceneContentMin.y);
    sceneWidth = static_cast<int>(sceneContentSize.x);
    sceneHeight = static_cast<int>(sceneContentSize.y);
    // render the EditorScene framebuffer
    //  Before calling ImGui::Image, flip the UVs by using ImGui::Image with custom UV coordinates:
    ImGui::Image(
        (ImTextureID)Editor::EditorState::GetInstance().GetEditorCamera()->GetComponent<Camera>()->m_pFrameBuffer->GetTextureID(),
        ImVec2(sceneContentSize.x, sceneContentSize.y),
        ImVec2(0, 1), // UV0: bottom-left
        ImVec2(1, 0)  // UV1: top-right
    );

    // Instead of ImGui::GetWindowPos() and ImGui::GetWindowSize(), use ImGui::IsWindowHovered() for better performance:
    Editor::EditorState::GetInstance().bIsSceneEditorHovered = ImGui::IsWindowHovered(ImGuiHoveredFlags_AllowWhenBlockedByActiveItem);

    // Update EditorGizmos with scene view information for proper mouse picking
    Editor::EditorGizmos::SetSceneViewInfo(sceneContentPos.x, sceneContentPos.y, sceneContentSize.x, sceneContentSize.y);

    ImGui::End();

    ImGui::End(); // End DockSpace window

    // Shadow Map Debug Window
    ImGui::Begin("Shadow Map Debug");
    ImGui::Text("Real-time Shadow Map (R32F)");

    // Display the blurred shadow map texture
    GLuint shadowTexture = Renderer::m_pBlurredShadowBuffer->GetTextureID();
    ImGui::Image(
        (ImTextureID)(uintptr_t)shadowTexture,
        ImVec2(256, 256),  // Size of the debug image
        ImVec2(0, 1),      // UV0: bottom-left (flip Y for OpenGL)
        ImVec2(1, 0)       // UV1: top-right
    );

    ImGui::Text("White = Far from light");
    ImGui::Text("Black = Close to light");
    ImGui::Text("This should change as you move the camera!");
    ImGui::End();

    // HBAO Debug Window
    ImGui::Begin("HBAO Settings");
    
    static float hbaoRadius = 10.0f;
    static float hbaoBias = 0.025f;
    static float hbaoIntensity = 1.0f;
    static int hbaoDirections = 8;
    static int hbaoSteps = 4;
    
    ImGui::SliderFloat("Radius", &hbaoRadius, 5.0f, 50.0f);
    ImGui::SliderFloat("Bias", &hbaoBias, 0.001f, 0.1f);
    ImGui::SliderFloat("Intensity", &hbaoIntensity, 0.0f, 3.0f);
    ImGui::SliderInt("Directions", &hbaoDirections, 4, 16);
    ImGui::SliderInt("Steps", &hbaoSteps, 2, 8);
    
    // Store these in a global struct or pass them to the renderer
    Editor::EditorState::GetInstance().GetEditorCamera()->GetComponent<Camera>()->hbaoRadius = hbaoRadius;
    Editor::EditorState::GetInstance().GetEditorCamera()->GetComponent<Camera>()->hbaoBias = hbaoBias;
    Editor::EditorState::GetInstance().GetEditorCamera()->GetComponent<Camera>()->hbaoIntensity = hbaoIntensity;
    Editor::EditorState::GetInstance().GetEditorCamera()->GetComponent<Camera>()->hbaoDirections = hbaoDirections;
    Editor::EditorState::GetInstance().GetEditorCamera()->GetComponent<Camera>()->hbaoSteps = hbaoSteps;
    
    // Display the HBAO texture for debugging
    GLuint hbaoTexture = Editor::EditorState::GetInstance().GetEditorCamera()->GetComponent<Camera>()->m_pHBAOBuffer->GetTextureID();
    ImGui::Image(
        (ImTextureID)(uintptr_t)hbaoTexture,
        ImVec2(256, 256),
        ImVec2(0, 1),
        ImVec2(1, 0)
    );
    
    ImGui::End();

    ImGuiNodeEditorWindow();

    ImGui::Render();

    ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());
}

int main(int argc, char *argv[])
{
    Renderer::Init();

    // setup imgui function pointer
    Renderer::imguiRenderFunction = RenderIMGUI;

    auto scene = std::make_shared<Scene>();
    scene->SetName("MainScene");
    Scene::SetActiveScene(scene);
    Renderer::SetActiveScene(scene.get());

    AddItems(scene.get());

    scene->Start();

    Editor::InitializeEditor();
    Editor::EditorState::GetInstance().SetActiveScene(scene);

    while (!Renderer::ShouldClose())
    {

        Time::Update();

        InputManager::Update();
        glfwPollEvents();

        Editor::Update();

        scene->Update();

        // get the sceneview visible area size from imgui
        auto editorCamera = Editor::EditorState::GetInstance().GetEditorCamera()->GetComponent<Camera>().get();
        Renderer::RenderEditorSceneView(editorCamera, glm::vec2(sceneWidth, sceneHeight));
    }

    Renderer::Shutdown();

    glfwDestroyWindow(Renderer::GetWindowPtr());
    glfwTerminate();
    return 0;
}
