#include "Camera.h"
#include "Renderer/Renderer.h"
#include "Components/Transform.h"

Camera::Camera()
{
    name = "Camera";
    backgroundColor = Color(0.0f, 0.0f, 0.0f); // Default background color (black)
    fov = 45.0f; // Field of view
    nearClip = 0.1f; // Near clipping plane
    farClip = 100.0f; // Far clipping plane
    aspectRatio = 1.0f;
    
    //#TODO: fix this camera name generation
    // This is a temporary solution to avoid name collision
    int cameraCount = Renderer::GetCameraCount();
    std::string cameraName = "Camera" + std::to_string(cameraCount);
    name = cameraName;
    go = nullptr; // Initialize gameObject to nullptr
    Renderer::AddCamera(this); // Add this camera to the renderer's camera list


}
Camera::~Camera()
{
    //set everything to nullptr
    name = "";
    backgroundColor = Color(0.0f, 0.0f, 0.0f);
    fov = 0.0f;
    nearClip = 0.0f;
    farClip = 0.0f;
    aspectRatio = 0.0f;
    m_CameraType = CameraType::PERSPECTIVE; // Reset camera type to default
    if (go)
    {
        go = nullptr; // Reset gameObject to nullptr
    }
    Renderer::RemoveCamera(this); // Remove this camera from the renderer's camera list
    Debug::Log(ReturnTypes::SUCCESS, "Camera::~Camera() - Camera component destroyed");
}
void Camera::Update()
{
    if(!go)
    {
        go = GetGameObject();
    }

    if (go)
    {
        SoundManager::GetInstance().SetListenerPosition(go->GetTransform()->GetPosition(),
                                                         go->GetTransform()->GetForwardVector(),
                                                         go->GetTransform()->GetUpVector());
        // Update the camera's position and orientation based on the GameObject's transform
        glm::vec3 position = go->GetTransform()->GetPosition();
        glm::vec3 front = go->GetTransform()->GetForwardVector();
        glm::vec3 up = go->GetTransform()->GetUpVector();

        Renderer::cameraPos = position;
        Renderer::cameraFront = front;
        Renderer::cameraUp = up;

        // Update the view matrix
        Renderer::viewMatrix = glm::lookAt(Renderer::cameraPos, Renderer::cameraPos + Renderer::cameraFront, Renderer::cameraUp);
    }

}

void Camera::Start()
{
    #ifdef DEBUG
    printf("Camera::Start() - Camera started\n");
    #endif
    // Initialize camera settings here (if needed)
    // projectionMatrix = glm::perspective(glm::radians(fov), aspectRatio, nearClip, farClip);
    // viewMatrix = glm::lookAt(Renderer::cameraPos, Renderer::cameraPos + Renderer::cameraFront, Renderer::cameraUp);

}

void Camera::Render()
{
    // Render logic here (if needed)
}

void Camera::PreRender()
{
    // Pre-render logic here (if needed)
}

void Camera::PostRender()
{
    // Post-render logic here (if needed)
}