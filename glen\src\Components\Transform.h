#pragma once

#include "glm/vec3.hpp"
#include "glm/mat4x4.hpp"
#include "glm/glm.hpp"
#include "glm/gtx/quaternion.hpp"

#include <Component.h>

class Transform : public Component
{
public:


	glm::vec3 m_vPosition = glm::vec3(0.0f, 0.0f, 0.0f);
	glm::vec3 m_vRotation = glm::vec3(0.0f, 0.0f, 0.0f);
	glm::vec3 m_vScale = glm::vec3(1.0f, 1.0f, 1.0f);

	Transform() { name = "Transform"; }
	Transform(const Transform&) = default;
	Transform(const glm::vec3& position)
		: m_vPosition(position) {name = "Transform";}

	glm::mat4 GetLocalMatrix() const
	{
		return glm::translate(glm::mat4(1.0f), m_vPosition)
			* glm::toMat4(glm::quat(m_vRotation))
			* glm::scale(glm::mat4(1.0f), m_vScale);
	}

	glm::mat4 GetLocalToWorldMatrix() const
	{
		glm::mat4 localMatrix = GetLocalMatrix();

		// If this GameObject has a parent, multiply by parent's world matrix
		if (go && go->GetParent())
		{
			auto parentTransform = go->GetParent()->GetTransform();
			return parentTransform->GetLocalToWorldMatrix() * localMatrix;
		}

		// No parent, so local matrix is the world matrix
		return localMatrix;
	}

	// Local direction vectors
	glm::vec3 GetForwardVector() const
	{
		return glm::quat(m_vRotation) * glm::vec3(0, 0, -1);
	}

	glm::vec3 GetRightVector() const
	{
		return glm::normalize(glm::cross(GetForwardVector(), glm::vec3(0, 1, 0)));
	}

	glm::vec3 GetUpVector() const
	{
		return glm::normalize(glm::cross(GetRightVector(), GetForwardVector()));
	}

	// World direction vectors (considering parent transforms)
	glm::vec3 GetWorldForwardVector() const
	{
		glm::mat4 worldMatrix = GetLocalToWorldMatrix();
		glm::vec3 forward = glm::vec3(worldMatrix * glm::vec4(0, 0, -1, 0));
		return glm::normalize(forward);
	}

	glm::vec3 GetWorldRightVector() const
	{
		glm::mat4 worldMatrix = GetLocalToWorldMatrix();
		glm::vec3 right = glm::vec3(worldMatrix * glm::vec4(1, 0, 0, 0));
		return glm::normalize(right);
	}

	glm::vec3 GetWorldUpVector() const
	{
		glm::mat4 worldMatrix = GetLocalToWorldMatrix();
		glm::vec3 up = glm::vec3(worldMatrix * glm::vec4(0, 1, 0, 0));
		return glm::normalize(up);
	}

	// Local transform setters/getters
	void SetPosition(const glm::vec3& position) { m_vPosition = position; }
	glm::vec3 GetPosition() const { return m_vPosition; }

	void SetRotation(const glm::vec3& rotation) { m_vRotation = rotation; }
	glm::vec3 GetRotation() const { return m_vRotation; }

	void SetScale(const glm::vec3& scale) { m_vScale = scale; }
	glm::vec3 GetScale() const { return m_vScale; }

	// World transform getters
	glm::vec3 GetWorldPosition() const
	{
		glm::mat4 worldMatrix = GetLocalToWorldMatrix();
		return glm::vec3(worldMatrix[3]);
	}

	glm::vec3 GetWorldRotation() const
	{
		// For now, return local rotation - proper world rotation extraction is complex
		// TODO: Extract rotation from world matrix properly
		if (go && go->GetParent())
		{
			// Simple approximation - add parent's world rotation
			auto parentTransform = go->GetParent()->GetTransform();
			return m_vRotation + parentTransform->GetWorldRotation();
		}
		return m_vRotation;
	}

	glm::vec3 GetWorldScale() const
	{
		// For now, return local scale - proper world scale extraction is complex
		// TODO: Extract scale from world matrix properly
		if (go && go->GetParent())
		{
			// Simple approximation - multiply by parent's world scale
			auto parentTransform = go->GetParent()->GetTransform();
			return m_vScale * parentTransform->GetWorldScale();
		}
		return m_vScale;
	}

	void Translate(const glm::vec3& translation)
	{
		m_vPosition += translation;
	}

	void Rotate(const glm::vec3& rotationDegrees)
	{
		m_vRotation += glm::radians(rotationDegrees);
	}

	void Scale(const glm::vec3& scale)
	{
		m_vScale *= scale;
	}

	void Update() override;
	void Start() override;

	//Get
	std::vector<BuiltInFieldInfo> GetBuiltInFields() override;
};

