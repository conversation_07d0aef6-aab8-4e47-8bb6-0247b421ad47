#version 450

in VS_OUT {
    vec3 FragPos;
    vec3 Normal;
    vec2 TexCoords;
    vec4 FragPosLightSpace;
    vec3 fragNormal;
    vec3 fragTangent;
    vec4 fragColor;
} fs_in;

layout (location = 0) out vec4 outColor;

//POINT LIGHT STUFF
#define MAX_LIGHTS 10

struct Light
{
    vec3 position;
    vec3 color;
    float intensity;
    float radius;
    int type; // 0: Directional, 1: Point
    float shadowBias; // Shadow bias for this light
    bool shadowEnabled;
};

uniform float far_plane; // Add this uniform for point light shadow


uniform sampler2D albedo;
uniform sampler2D shadowMap; // Blurred shadow map
uniform sampler2D normalMap;
uniform samplerCube pointShadowMap[MAX_LIGHTS];

uniform vec4 mainColor;
uniform vec3 ambientLight; // Ambient light color
uniform bool discardTransparent; // Control whether to discard transparent fragments
uniform vec3 mainDirectionalLightDirection; // Directional Light direction
uniform vec3 mainDirectionalLightColor; // Directional Light color
uniform float mainDirectionalLightIntensity; // Directional Light intensity
uniform vec3 viewPos; // View position
uniform float shadowBias; // Shadow bias

//POINT LIGHT STUFF
uniform Light lights[MAX_LIGHTS];
uniform int lightCount; // Number of lights in the scene

const float weights[5] = float[](0.227027, 0.1945946, 0.1216216, 0.054054, 0.016216);

float rand(vec2 co) {
    return fract(sin(dot(co.xy ,vec2(12.9898,78.233))) * 43758.5453);
}

// Poisson Disk
vec2 poissonDisk[16] = vec2[](
vec2(-0.94201624, -0.39906216),
vec2(0.94558609, -0.76890725),
vec2(-0.094184101, -0.92938870),
vec2(0.34495938, 0.29387760),
vec2(-0.91588581, 0.45771432),
vec2(-0.81544232, -0.87912464),
vec2(-0.38277543, 0.27676845),
vec2(0.97484398, 0.75648379),
vec2(0.44323325, -0.97511554),
vec2(0.53742981, -0.47373420),
vec2(-0.26496911, -0.41893023),
vec2(0.79197514, 0.19090188),
vec2(-0.24188840, 0.99706507),
vec2(-0.81409955, 0.91437590),
vec2(0.19984126, 0.78641367),
vec2(0.14383161, -0.14100790)
);




float VSMDirectionalShadow(vec4 fragPosLightSpace)
{
    // Project to light space
    vec3 projCoords = fragPosLightSpace.xyz / fragPosLightSpace.w;
    projCoords = projCoords * 0.5 + 0.5;

    // Sample the VSM shadow map (RG32F format)
    vec2 moments = texture(shadowMap, projCoords.xy).rg;
    float depth = projCoords.z;

    // Basic depth test
    if (depth <= moments.x) {
        return 1.0; // Fully lit
    }

    // Calculate variance
    float variance = moments.y - (moments.x * moments.x);
    variance = max(variance, 0.00002); // Minimum variance to avoid artifacts

    // Calculate probability using Chebyshev's inequality
    float d = depth - moments.x;
    float p_max = variance / (variance + d * d);

    // Light bleeding reduction
    float amount = 0.3; // Adjust this value (0.0 to 1.0)
    p_max = clamp((p_max - amount) / (1.0 - amount), 0.0, 1.0);

    return p_max;
}

float DirectionalLightShadowCalculationBlur(vec4 fragPosLightSpace)
{
    // Perform perspective divide
    vec3 projCoords = fragPosLightSpace.xyz / fragPosLightSpace.w;
    // Transform to [0,1] range
    projCoords = projCoords * 0.5 + 0.5;
    // Get depth of current fragment from light's perspective
    float currentDepth = projCoords.z;

    // Gaussian blur
    float shadow = 0.0;
    vec2 texelSize = 1.0 / textureSize(shadowMap, 0);
    for (int i = -4; i <= 4; ++i)
    {
        for (int j = -4; j <= 4; ++j)
        {
            float weight = weights[abs(i)] * weights[abs(j)];
            float pcfDepth = texture(shadowMap, projCoords.xy + vec2(i, j) * texelSize).r;
            shadow += weight * (currentDepth > pcfDepth + shadowBias ? 1.0 : 0.0);
        }
    }
    
    float jitterAmount = 0.35; // Tweak for softness
    vec2 jitter = (rand(gl_FragCoord.xy) - 0.5) * jitterAmount * texelSize;

    for (int i = -4; i <= 4; ++i)
    {
        for (int j = -4; j <= 4; ++j)
        {
            float weight = weights[abs(i)] * weights[abs(j)];
            vec2 offset = vec2(i, j) * texelSize + jitter;
            float pcfDepth = texture(shadowMap, projCoords.xy + offset).r;
            shadow += weight * (currentDepth > pcfDepth + shadowBias ? 1.0 : 0.0);
        }
    }

    

    return shadow;
}

float DirectionalLightShadowCalculation(vec4 fragPosLightSpace)
{
    // Perform perspective divide
    vec3 projCoords = fragPosLightSpace.xyz / fragPosLightSpace.w;
    // Transform to [0,1] range
    projCoords = projCoords * 0.5 + 0.5;
    // Get depth of current fragment from light's perspective
    float currentDepth = projCoords.z;

    // Simple shadow map lookup (no blur)
    float pcfDepth = texture(shadowMap, projCoords.xy).r;
    float shadow = currentDepth > pcfDepth + shadowBias ? 1.0 : 0.0;

    

    return shadow + shadowBias; // Return shadow with bias applied
}


// VSM Point light shadow calculation using cubemap
float VSMPointShadowCalculation(vec3 fragPos, int lightIndex, out float outFade)
{
    vec3 fragToLight = fragPos - lights[lightIndex].position;
    float currentDepth = length(fragToLight);

    // Normalize depth by light radius (same as in VSM depth shader)
    float normalizedDepth = currentDepth / lights[lightIndex].radius;

    // Sample VSM moments from cube map (RG32F format)
    vec2 moments = texture(pointShadowMap[lightIndex], fragToLight).rg;

    // Basic depth test
    if (normalizedDepth <= moments.x) {
        outFade = smoothstep(lights[lightIndex].radius * 0.65, lights[lightIndex].radius, currentDepth);
        return 0.0; // Fully lit (no shadow)
    }

    // Calculate variance
    float variance = moments.y - (moments.x * moments.x);
    variance = max(variance, 0.00002); // Minimum variance to avoid artifacts

    // Calculate probability using Chebyshev's inequality
    float d = normalizedDepth - moments.x;
    float p_max = variance / (variance + d * d);

    // Light bleeding reduction
    float amount = 0.3; // Adjust this value (0.0 to 1.0)
    p_max = clamp((p_max - amount) / (1.0 - amount), 0.0, 1.0);

    // Compute fade factor for blending to black outside the range
    float fade = smoothstep(lights[lightIndex].radius * 0.65, lights[lightIndex].radius, currentDepth);
    outFade = fade;

    // If outside the range, force shadow to 1.0 (fully shadowed)
    if(currentDepth >= lights[lightIndex].radius)
    {
        return 1.0; // Fully shadowed
    }

    return 1.0 - p_max; // Invert VSM result: 1.0 = shadowed, 0.0 = lit
}

vec3 normalFromGreen(vec2 texCoords) {
    float g = texture(albedo, texCoords).g;
    // Map green from [0,1] to [-1,1] for Y
    float y = g * 2.0 - 1.0;
    return normalize(vec3(0.0, y, 1.0));
}

// Generate a basic tangent space normal from the albedo texture using a Sobel filter for height and a depth scale
vec3 normalFromAlbedoSobel(vec2 texCoords, float depthScale)
{
    // Sobel kernels for X and Y
    float kernelX[9] = float[](
        -1, 0, 1,
        -2, 0, 2,
        -1, 0, 1
    );
    float kernelY[9] = float[](
        -1, -2, -1,
         0,  0,  0,
         1,  2,  1
    );

    // Sample offsets (assuming texture coordinates in [0,1])
    vec2 texelSize = 1.0 / vec2(textureSize(albedo, 0));
    float height[9];
    int idx = 0;
    for (int y = -1; y <= 1; ++y) {
        for (int x = -1; x <= 1; ++x) {
            vec2 offset = vec2(float(x), float(y)) * texelSize;
            // Use luminance or just the green channel for height
            float h = texture(albedo, texCoords + offset).g;
            height[idx++] = h;
        }
    }

    // Apply Sobel filter
    float dX = 0.0;
    float dY = 0.0;
    for (int i = 0; i < 9; ++i) {
        dX += height[i] * kernelX[i];
        dY += height[i] * kernelY[i];
    }

    // Construct normal in tangent space
    vec3 normal;
    normal.x = -dX * depthScale;
    normal.y = -dY * depthScale;
    normal.z = 1.0;
    return normalize(normal);
}

void main() {

    vec4 texColor = texture(albedo, fs_in.TexCoords);

    if (discardTransparent && texColor.a < 0.1)
    {
        discard;
    }

    vec3 normalMap = texture(normalMap, fs_in.TexCoords).rgb;
    normalMap = normalMap * 2.0 - 1.0;

    normalMap = normalFromAlbedoSobel(fs_in.TexCoords, 0.8); // Use Sobel filter for normal map
    // Ensure normalMap is in the range [-1, 1]
    normalMap = normalize(normalMap);

    vec3 T = normalize(fs_in.fragTangent);
    vec3 N = normalize(fs_in.fragNormal);
    vec3 B = cross(N, T);
    mat3 TBN = mat3(T, B, N);

    vec3 normal = normalize(TBN * normalMap);
    vec3 viewDir = normalize(viewPos - fs_in.FragPos);

    vec3 totalDiffuse = vec3(0.0);
    vec3 totalSpecular = vec3(0.0);

    for (int lightIndex = 0; lightIndex < lightCount; ++lightIndex)
    {
        vec3 lightDir = normalize(lights[lightIndex].position - fs_in.FragPos);
        float distance = length(lights[lightIndex].position - fs_in.FragPos);
        float attenuation = 1.0f;

        float diff = max(dot(normal, lightDir), 0.0);
        vec3 diffuse = diff * lights[lightIndex].color * attenuation;

        // Only compute specular if facing the light
        float specularStrength = 0.5;
        float shininess = 32.0;
        float spec = 0.0;
        if(diff > 0.0)
        {
            vec3 reflectDir = reflect(-lightDir, normal);
            spec = pow(max(dot(viewDir, reflectDir), 0.0), shininess);
        }
        vec3 specular = specularStrength * spec * vec3(1.0, 1.0, 1.0) * attenuation;

        // Calculate shadow for this light using VSM
        float fade;
        float pointShadow = VSMPointShadowCalculation(fs_in.FragPos, lightIndex, fade);

        if(!lights[lightIndex].shadowEnabled)
        {
            pointShadow = 0.0; // No shadow if shadow is disabled
        }

        // Smoothly blend shadow using smoothstep
        float shadowFactor = smoothstep(0.4, 0.6, pointShadow);

        // Fade out the light smoothly as we approach far_plane
        float lightVisibility = 1.0 - max(shadowFactor, fade);

        totalDiffuse += diffuse * lightVisibility;
        totalSpecular += specular * lightVisibility;
    }

    // --- Directional light contribution with shadow ---
    vec3 dirLightDir = normalize(-mainDirectionalLightDirection); // Negate because we want light direction, not surface-to-light
    float dirDiff = max(dot(normal, dirLightDir), 0.0);
    vec3 dirDiffuse = dirDiff * mainDirectionalLightColor * mainDirectionalLightIntensity;
    

    float specularStrength = 0.5;
    float shininess = 32.0;

    float dirSpec = 0.0;
    
    if(dirDiff > 0.0)
    {
        vec3 dirReflectDir = reflect(-dirLightDir, normal);
        dirSpec = pow(max(dot(viewDir, dirReflectDir), 0.0), shininess);
    }

    vec3 dirSpecular = specularStrength * dirSpec * vec3(1.0, 1.0, 1.0); // Diffuse color

    // Calculate shadow for directional light using VSM
    float dirShadow = VSMDirectionalShadow(fs_in.FragPosLightSpace);

    float dirLightVisibility = clamp(dirShadow, 0.0, 1.0);

    totalDiffuse += dirDiffuse * dirLightVisibility;
    totalSpecular += dirSpecular * dirLightVisibility;

    // Final color
    vec3 tintedColor = texColor.rgb * mainColor.rgb;
    vec3 finalColor = tintedColor * fs_in.fragColor.rgb * (totalDiffuse + totalSpecular + ambientLight);

    outColor = vec4(finalColor, 1.0);
}