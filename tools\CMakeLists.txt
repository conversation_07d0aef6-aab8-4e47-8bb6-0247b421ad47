cmake_minimum_required(VERSION 3.25)

project(glenEdit VERSION 0.0.1)

set(CMAKE_CXX_STANDARD 17)

# Set static linking flags for Windows (optional)
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -static-libgcc -static-libstdc++ -static")

# ========= Paths =========
set(SRC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../src)
set(RES_DIR ${CMAKE_CURRENT_SOURCE_DIR}/resources)
#set(OUT_DIR ${CMAKE_CURRENT_SOURCE_DIR}/bin/${CMAKE_BUILD_TYPE})
set(OUT_DIR ${CMAKE_CURRENT_SOURCE_DIR}/Editor/bin)

# ========= Editor Executable =========
add_executable(${PROJECT_NAME}
    Editor/src/main.cpp
    Editor/src/Editor/Editor.cpp
    Editor/src/Editor/EditorGizmos.cpp
    Editor/src/Editor/EditorGizmosPicking.cpp
    Editor/src/Editor/EditorGizmosRendering.cpp
    Editor/src/Editor/EditorGizmosInteraction.cpp
    Editor/src/Editor/EditorGizmosColorPicking.cpp
    Editor/src/GUI/UI_SceneHierarchy.cpp
    Editor/src/GUI/UI_Inspector.cpp
)

set(EDITOR_COMPONENTS "IMGUI" CACHE STRING "The components to import and build")

foreach (comp IN ITEMS ${EDITOR_COMPONENTS})
    set(IMPORT_${comp} ON)
endforeach ()

if (IMPORT_IMGUI)
    FetchContent_Declare(
            imgui
            GIT_REPOSITORY https://github.com/ocornut/imgui.git
            GIT_TAG docking
    )
    list(APPEND components imgui)
endif ()

if (components)
    FetchContent_MakeAvailable(${components})
endif ()

##############################
#            imgui           #
##############################
if (IMPORT_IMGUI)
    file(GLOB imgui_SRC ${imgui_SOURCE_DIR}/*.cpp ${imgui_SOURCE_DIR}/*.h)
    add_library(
            imgui
            ${imgui_SRC}
            ${imgui_SOURCE_DIR}/backends/imgui_impl_opengl3.cpp
            ${imgui_SOURCE_DIR}/backends/imgui_impl_opengl3.h
            ${imgui_SOURCE_DIR}/backends/imgui_impl_glfw.cpp
            ${imgui_SOURCE_DIR}/backends/imgui_impl_glfw.h
    )

    target_link_libraries(imgui PUBLIC glad)
    if (NOT EMSCRIPTEN)
        target_link_libraries(imgui PUBLIC glfw)
    endif ()
    target_include_directories(
            imgui
            PUBLIC
            ${imgui_SOURCE_DIR}
            ${imgui_SOURCE_DIR}/backends
    )
endif ()

target_include_directories(${PROJECT_NAME} PRIVATE ${SRC_DIR})
target_link_libraries(${PROJECT_NAME} PRIVATE glen imgui)

# Output directory for editor
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${OUT_DIR}
)

## ========= MyComponent DLL (Plugin) =========
#add_library(glen-game SHARED
#    dllmain.cpp
#    MyComponent.cpp
#    MyComponent2.cpp
#)
#
#target_include_directories(glen-game PRIVATE ${SRC_DIR})
#target_link_libraries(glen-game PRIVATE glen)
#
## Set output directory for the DLL
#set_target_properties(glen-game PROPERTIES
#    RUNTIME_OUTPUT_DIRECTORY ${OUT_DIR}
#    LIBRARY_OUTPUT_DIRECTORY ${OUT_DIR}
#    ARCHIVE_OUTPUT_DIRECTORY ${OUT_DIR}
#    OUTPUT_NAME "glen-game" # Ensures DLL is named cleanly
#    PREFIX "" # Removes the "lib" prefix
#    #link gcc libraries statically
#    LINK_FLAGS "-static-libgcc -static-libstdc++"
#)

# ========= Copy Resources =========
file(COPY ${RES_DIR}/ DESTINATION ${OUT_DIR}/resources)


if (WIN32)
    set_target_properties(${PROJECT_NAME} PROPERTIES
        WIN32_EXECUTABLE $<IF:$<CONFIG:Release,MinSizeRel>,ON,OFF>
    )
endif ()

#if msvc /SUBSYSTEM:CONSOLE
if (MSVC)
    set_target_properties(${PROJECT_NAME} PROPERTIES
        LINK_FLAGS "/SUBSYSTEM:CONSOLE"
    )
endif ()