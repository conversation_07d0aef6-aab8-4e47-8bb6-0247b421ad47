import bpy
import bmesh
import struct

def export_meshes(filepath):
    selected_objects = [obj for obj in bpy.context.selected_objects if obj.type == 'MESH']
    if not selected_objects:
        print("No mesh objects selected.")
        return

    mesh_data = []

    for obj in selected_objects:
        # Duplicate the object and apply transformations and modifiers
        bpy.ops.object.select_all(action='DESELECT')
        obj.select_set(True)
        bpy.context.view_layer.objects.active = obj
        bpy.ops.object.duplicate()
        bpy.ops.object.transform_apply(location=True, rotation=True, scale=True)
        obj = bpy.context.active_object

        # Create a BMesh to handle triangulation
        bm = bmesh.new()
        bm.from_mesh(obj.data)

        # Triangulate the mesh
        bmesh.ops.triangulate(bm, faces=bm.faces)
        bm.to_mesh(obj.data)
        bm.free()

        mesh = obj.data

        vertices = []
        indices = []

        # Ensure the mesh has tangents and UVs
        if not mesh.uv_layers.active:
            print(f"Mesh {obj.name} has no UV map.")
            bpy.ops.object.delete()
            continue

        mesh.calc_tangents()

        uv_layer = mesh.uv_layers.active.data

        vertex_map = {}
        index = 0

        for poly in mesh.polygons:
            # Ensure consistent face orientation by using polygon normals
            face_normal = poly.normal
            for loop_index in poly.loop_indices:
                loop = mesh.loops[loop_index]
                vert = mesh.vertices[loop.vertex_index]

                vertex = vert.co
                normal = vert.normal.normalized()

                # Flip the normal if it opposes the face normal
                if normal.dot(face_normal) < 0:
                    normal = -normal

                tangent = loop.tangent
                uv = uv_layer[loop.index].uv

                # Key to identify unique vertex data
                key = (
                    vertex.x, vertex.y, vertex.z,
                    normal.x, normal.y, normal.z,
                    tangent.x, tangent.y, tangent.z,
                    uv.x, uv.y
                )

                if key not in vertex_map:
                    vertex_map[key] = index
                    # Store all vertex attributes together in order
                    vertices.append((
                        vertex.x, vertex.y, vertex.z,  # Position
                        normal.x, normal.y, normal.z,  # Normal
                        tangent.x, tangent.y, tangent.z,  # Tangent
                        uv.x, uv.y,  # Texture Coordinates
                        1.0, 1.0, 1.0, 1.0  # Default white vertex color
                    ))
                    index += 1

                indices.append(vertex_map[key])

        # Get the material name (texture) and blend mode
        material_name = obj.active_material.name if obj.active_material else "None"
        blend_mode = obj.active_material.blend_method if obj.active_material else 'OPAQUE'
        is_transparent = blend_mode != 'OPAQUE'

        mesh_data.append((vertices, indices, material_name, is_transparent))

        # Delete the duplicate object
        bpy.ops.object.delete()

    with open(filepath, 'wb') as f:
        # Write number of meshes
        f.write(struct.pack('I', len(mesh_data)))

        for vertices, indices, material_name, is_transparent in mesh_data:
            # Write material name length and material name
            f.write(struct.pack('I', len(material_name)))
            f.write(material_name.encode('utf-8'))

            # Write transparency flag
            f.write(struct.pack('?', is_transparent))

            # Write number of vertices
            f.write(struct.pack('I', len(vertices)))

            # Write all vertex attributes together
            for v in vertices:
                f.write(struct.pack('3f 3f 3f 2f 4f', *v))

            # Write number of indices
            f.write(struct.pack('I', len(indices)))

            # Write indices
            for i in indices:
                f.write(struct.pack('I', i))

    print(f"Meshes exported to {filepath}")

# Example usage
export_meshes("C:/Users/<USER>/source/repos/glen/resources/models/meshes.bin")