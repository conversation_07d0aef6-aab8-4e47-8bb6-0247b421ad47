#version 450 core

layout(location = 0) out vec2 FragMoments;

uniform sampler2D albedo;
uniform bool discardTransparent;

in vec2 TexCoords;

void main()
{
    // Always sample the diffuse texture for alpha discard
    vec4 texColor = texture(albedo, TexCoords);

    // Discard transparent fragments
    if (discardTransparent && texColor.a < 0.1) {
        discard;
    }

    // Calculate depth moments for VSM
    float depth = gl_FragCoord.z;
    float moment1 = depth;
    float moment2 = depth * depth;

    // Store moments in RG channels
    FragMoments = vec2(moment1, moment2);
}
