#pragma once

#include "Component.h"
#include "Components/Transform.h"
#include "SoundManager.h"

class Sound : public Component
{
public:
    Sound()
    {
        name = "Sound";
    }
    Sound(const std::string& name)
    {
        this->name = "Sound";
        m_soundProperties.name = name;
    }

    void Play()
    {
        if(m_soundProperties.Is3D)
        {
            SoundManager::GetInstance().PlayAt(m_soundProperties, go->GetTransform()->GetPosition());
        }
        else
        {
            SoundManager::GetInstance().Play(m_soundProperties);
        }
    }

    void Stop()
    {
        SoundManager::GetInstance().Stop(m_soundProperties);
    }

    void SetSoundProperties(const SoundManager::SoundProperties& soundProperties)
    {
        m_soundProperties = soundProperties;
    }

    void Set3D(bool is3D)
    {
        m_soundProperties.Is3D = is3D;
    }
    void SetLoop(bool loop)
    {
        m_soundProperties.Loop = loop; 
    }
    void SetVolume(float volume)
    {
        m_soundProperties.Volume = volume;
    }
    void SetPitch(float pitch)
    {
        m_soundProperties.Pitch = pitch;
    }
    void SetMinDistance(float minDistance)
    {
        m_soundProperties.MinDistance = minDistance;
    }
    void SetMaxDistance(float maxDistance)
    {
        m_soundProperties.MaxDistance = maxDistance;
    }
    void SetRolloffFactor(float rolloffFactor)
    {
        m_soundProperties.RolloffFactor = rolloffFactor;
    }
    void SetName(const std::string& name)
    {
        m_soundProperties.name = name;
    }

    ~Sound() override
    {
        Stop();
    }

	void Start() override
    {
        Play();
    }

	void Update() override
    {
        SoundManager::GetInstance().UpdateSoundData(m_soundProperties);
        SoundManager::GetInstance().UpdateSoundPosition(m_soundProperties, go->GetTransform()->GetPosition());
    };

	std::vector<BuiltInFieldInfo> GetBuiltInFields() override 
    { 
        std::vector<BuiltInFieldInfo> fields;
        fields.reserve(8);
        fields.push_back(BuiltInFieldInfo{"string", "FilePath", &m_soundProperties.name});
        fields.push_back(BuiltInFieldInfo{"bool", "Is3D", &m_soundProperties.Is3D});
        fields.push_back(BuiltInFieldInfo{"bool", "Loop", &m_soundProperties.Loop});
        fields.push_back(BuiltInFieldInfo{"float", "Volume", &m_soundProperties.Volume});
        fields.push_back(BuiltInFieldInfo{"float", "Pitch", &m_soundProperties.Pitch});
        fields.push_back(BuiltInFieldInfo{"float", "MinDistance", &m_soundProperties.MinDistance});
        fields.push_back(BuiltInFieldInfo{"float", "MaxDistance", &m_soundProperties.MaxDistance});
        fields.push_back(BuiltInFieldInfo{"float", "RolloffFactor", &m_soundProperties.RolloffFactor});
        return fields;
    }

private:
    SoundManager::SoundProperties m_soundProperties;
};
