#pragma once

#include <glen.h>
#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <vector>
#include <memory>

namespace Editor {

class EditorGizmos
{
public:
    // Gizmo interaction state enum moved to public so it can be used in method signatures
    enum class GizmoAxis { NONE, X, Y, Z, XY, XZ, YZ };

    static void Initialize();
    static void Shutdown();
    
    // Main rendering functions
    static void RenderGizmos(Camera* camera);
    static void RenderBillboards(Camera* camera);
    
    // Selection system
    static void HandleMousePicking(Camera* camera);
    static void SetSceneViewInfo(float x, float y, float width, float height);
    static int GetSelectedGameObjectIndex() { return s_selectedGameObjectIndex; }
    static void SetSelectedGameObjectIndex(int index);

    // Gizmo interaction
    static void HandleGizmoInteraction(Camera* camera);
    static void UpdateGizmoDrag(Camera* camera);

    // Color picking for gizmo interaction
    static void RenderGizmosForPicking(Camera* camera);
    static GizmoAxis GetAxisFromPickingColor(unsigned char r, unsigned char g, unsigned char b, bool debug = true);
    static GizmoAxis GetHoveredAxis(Camera* camera);
    
    // Gizmo rendering
    static void RenderLightGizmos(Camera* camera);
    static void RenderTransformGizmos(Camera* camera);
    static void RenderHitPointGizmo(Camera* camera);
    
    // Billboard rendering
    static void RenderBillboard(const glm::vec3& position, float size, const glm::mat4& view, 
                               const glm::mat4& projection, const glm::vec3& color = glm::vec3(1.0f), 
                               float alpha = 0.8f, GLuint textureID = 0);

    static float s_sceneViewX;
    static float s_sceneViewY;
    static float s_sceneViewWidth;
    static float s_sceneViewHeight;

private:
    // Selection state
    static int s_selectedGameObjectIndex;
    static bool s_hasHit;
    static glm::vec3 s_hitPoint;

    // Gizmo interaction state
    static GizmoAxis s_activeGizmoAxis;
    static bool s_isDragging;
    static glm::vec3 s_dragStartPos;
    static glm::vec3 s_objectStartPos;

    // Color picking framebuffer for gizmo interaction
    static std::unique_ptr<FrameBuffer> s_pickingFrameBuffer;
    
    // Gizmo geometry
    struct GizmoSphere {
        GLuint vao = 0, vbo = 0, ebo = 0;
        int indexCount = 0;
    };
    struct GizmoBox {
        GLuint vao = 0, vbo = 0, ebo = 0;
        int indexCount = 0;
    };
    struct GizmoArrow {
        GLuint vao = 0, vbo = 0, eboLines = 0, eboTris = 0;
        int lineIndexCount = 0;
        int triIndexCount = 0;
    };
    struct GizmoPlane {
        GLuint vao = 0, vbo = 0, ebo = 0;
        int indexCount = 0;
    };

    static GizmoSphere s_wireSphere;
    static GizmoBox s_wireBox;
    static GizmoArrow s_wireArrow;
    static GizmoPlane s_plane;
    
    // Billboard geometry
    static GLuint s_billboardVAO;
    static GLuint s_billboardVBO;
    static GLuint s_billboardEBO;
    static GLuint s_gridTextureID;
    
    // Helper functions
    static GizmoSphere CreateWireSphere(int segments);
    static GizmoBox CreateWireBox();
    static GizmoArrow CreateWireArrow();
    static GizmoPlane CreatePlane();
    static void CreateGridTexture();
    static void InitializeBillboardGeometry();
    static std::vector<Light*> GetLights();

};

} // namespace Editor
