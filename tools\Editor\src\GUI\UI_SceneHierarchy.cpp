#include <glen.h>
#include <imgui.h>
#include <imgui_internal.h>
#include <imgui_impl_glfw.h>
#include <imgui_impl_opengl3.h>
#include "../Editor/Editor.h"

static int selectedGameObjectIndex = -1;
static void DrawGameObjectNode(const std::shared_ptr<GameObject>& entity);
static void RenderSceneHierarchyContextMenu();
static bool IsDescendantOf(const std::shared_ptr<GameObject>& ancestor, const std::shared_ptr<GameObject>& potentialDescendant);

void RenderSceneHierarchyWindow()
{
    ImGui::Begin("Scene Hierarchy");

    Scene *activeScene = Renderer::s_pActiveScene;
    if (activeScene)
    {
        const auto &gameObjects = activeScene->GetGameObjects();
        for (size_t i = 0; i < gameObjects.size(); ++i)
        {
            const auto &entity = gameObjects[i];
            // Only draw root objects (no parent)
            if (entity->GetParent() == nullptr)
            {
                DrawGameObjectNode(entity);
            }
        }

        // Add drop target to the empty space in the hierarchy to make objects root objects
        ImGui::Dummy(ImVec2(0.0f, 50.0f)); // Create some empty space
        if (ImGui::BeginDragDropTarget())
        {
            // Show visual feedback
            if (const ImGuiPayload* payload = ImGui::GetDragDropPayload())
            {
                if (payload->IsDataType("GAMEOBJECT"))
                {
                    std::shared_ptr<GameObject> draggedObject = *(std::shared_ptr<GameObject>*)payload->Data;
                    if (draggedObject->GetParent()) // Only show tooltip if it's currently a child
                    {
                        ImGui::SetTooltip("Drop to make %s a root object", draggedObject->GetName().c_str());
                    }
                }
            }

            if (const ImGuiPayload* payload = ImGui::AcceptDragDropPayload("GAMEOBJECT"))
            {
                // Get the dragged GameObject
                std::shared_ptr<GameObject> draggedObject = *(std::shared_ptr<GameObject>*)payload->Data;

                // Remove from current parent
                auto currentParent = draggedObject->GetParent();
                if (currentParent)
                {
                    currentParent->RemoveChild(draggedObject);
                    // Add to scene root
                    activeScene->AddGameObject(draggedObject);
                }
                // If it's already a root object, no need to do anything
            }
            ImGui::EndDragDropTarget();
        }
    }

    ImGui::End();
}

void DrawGameObjectNode(const std::shared_ptr<GameObject>& entity)
{
    ImGuiTreeNodeFlags flags = ImGuiTreeNodeFlags_OpenOnArrow | ImGuiTreeNodeFlags_OpenOnDoubleClick;

    // Check if this entity is the currently selected one
    auto& editorState = Editor::EditorState::GetInstance();
    if (editorState.GetActiveGameObject() == entity)
        flags |= ImGuiTreeNodeFlags_Selected;

    bool hasChildren = !entity->GetChildren().empty();
    bool open = ImGui::TreeNodeEx(entity->GetName().c_str(), flags | (hasChildren ? 0 : ImGuiTreeNodeFlags_Leaf));

    // Handle selection
    if (ImGui::IsItemClicked())
    {
        // Set the active GameObject directly
        editorState.SetActiveGameObject(entity);

        // Also update the selectedGameObjectIndex for backward compatibility
        // Find the index of this GameObject in the scene's root objects list
        Scene *activeScene = Renderer::s_pActiveScene;
        if (activeScene)
        {
            const auto &gameObjects = activeScene->GetGameObjects();
            editorState.selectedGameObjectIndex = -1; // Default to -1 for child objects

            for (size_t i = 0; i < gameObjects.size(); ++i)
            {
                if (gameObjects[i] == entity)
                {
                    editorState.selectedGameObjectIndex = static_cast<int>(i);
                    break;
                }
            }
        }
    }

    // Drag and Drop Source
    if (ImGui::BeginDragDropSource(ImGuiDragDropFlags_None))
    {
        // Store the GameObject pointer as payload
        ImGui::SetDragDropPayload("GAMEOBJECT", &entity, sizeof(std::shared_ptr<GameObject>));
        ImGui::Text("Moving %s", entity->GetName().c_str());
        ImGui::EndDragDropSource();
    }

    // Drag and Drop Target
    if (ImGui::BeginDragDropTarget())
    {
        // Check if the payload is valid and not dropping on itself or descendants
        if (const ImGuiPayload* payload = ImGui::GetDragDropPayload())
        {
            if (payload->IsDataType("GAMEOBJECT"))
            {
                std::shared_ptr<GameObject> draggedObject = *(std::shared_ptr<GameObject>*)payload->Data;

                // Show visual feedback
                if (draggedObject != entity && !IsDescendantOf(draggedObject, entity))
                {
                    ImGui::SetTooltip("Drop to make %s a child of %s",
                                    draggedObject->GetName().c_str(),
                                    entity->GetName().c_str());
                }
                else
                {
                    ImGui::SetTooltip("Cannot drop here (invalid target)");
                }
            }
        }

        if (const ImGuiPayload* payload = ImGui::AcceptDragDropPayload("GAMEOBJECT"))
        {
            // Get the dragged GameObject
            std::shared_ptr<GameObject> draggedObject = *(std::shared_ptr<GameObject>*)payload->Data;

            // Prevent dropping an object onto itself or its own children
            if (draggedObject != entity && !IsDescendantOf(draggedObject, entity))
            {
                // Remove from current parent
                auto currentParent = draggedObject->GetParent();
                Scene *activeScene = Renderer::s_pActiveScene;

                if (currentParent)
                {
                    currentParent->RemoveChild(draggedObject);
                }
                else if (activeScene)
                {
                    // Remove from scene root if it was a root object
                    activeScene->RemoveGameObject(draggedObject);
                }

                // Add to new parent
                entity->AddChild(draggedObject);
            }
        }
        ImGui::EndDragDropTarget();
    }

    // Context menu for the entity
    RenderSceneHierarchyContextMenu();

    if (open && hasChildren)
    {
        const auto& children = entity->GetChildren();
        for (const auto& child : children)
        {
            DrawGameObjectNode(child);
        }
        ImGui::TreePop();
    }
    else if (open)
    {
        ImGui::TreePop();
    }
}

void RenderSceneHierarchyContextMenu()
{
    if (ImGui::BeginPopupContextWindow())
    {
        if (ImGui::MenuItem("Create Empty"))
        {
            auto newGameObject = std::make_shared<GameObject>("GameObject");

            auto SoundComponent = std::make_shared<Sound>("test");
            SoundComponent->SetVolume(100.0f);
            SoundComponent->SetLoop(true);
            SoundComponent->Set3D(false);
            newGameObject->AddComponent(SoundComponent);
            Scene *activeScene = Renderer::s_pActiveScene;
            if (activeScene)
            {
                activeScene->AddGameObject(newGameObject);
                // Clear selection
                auto& editorState = Editor::EditorState::GetInstance();
                editorState.SetActiveGameObject(nullptr);
                editorState.selectedGameObjectIndex = -1;
            }
        }
        if (ImGui::MenuItem("Delete"))
        {
            auto& editorState = Editor::EditorState::GetInstance();
            auto selectedObject = editorState.GetActiveGameObject();
            if (selectedObject)
            {
                Scene *activeScene = Renderer::s_pActiveScene;
                if (activeScene)
                {
                    // Check if it's a root object (can be removed from scene)
                    if (selectedObject->GetParent() == nullptr)
                    {
                        activeScene->RemoveGameObject(selectedObject);
                    }
                    else
                    {
                        // It's a child object, remove it from its parent
                        auto parent = selectedObject->GetParent();
                        if (parent)
                        {
                            parent->RemoveChild(selectedObject);
                        }
                    }

                    // Clear selection
                    editorState.SetActiveGameObject(nullptr);
                    editorState.selectedGameObjectIndex = -1;
                }
            }
        }
        ImGui::EndPopup();
    }
}

bool IsDescendantOf(const std::shared_ptr<GameObject>& ancestor, const std::shared_ptr<GameObject>& potentialDescendant)
{
    if (!potentialDescendant || !ancestor)
        return false;

    // Check if potentialDescendant is a child of ancestor
    const auto& children = ancestor->GetChildren();
    for (const auto& child : children)
    {
        if (child == potentialDescendant)
            return true;

        // Recursively check children
        if (IsDescendantOf(child, potentialDescendant))
            return true;
    }

    return false;
}
