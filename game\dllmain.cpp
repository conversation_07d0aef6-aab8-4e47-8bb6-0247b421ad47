#include <string>
#include <GL/gl.h>
#undef __gl_h_
#include <glad/gl.h>
#include <unordered_map>
#include "MyComponent.h" // Base class for your component
#include "MyComponent2.h" // Base class for your component


// Factory function
extern "C" __declspec(dllexport) IComponent* CreateComponent(const char* className)
{
    std::string name(className);

    if (name == "MyComponent")
        return new MyComponent();
    if (name == "MyComponent2")
        return new MyComponent2();

    // Add more classes as needed

    return nullptr; // Unknown class
}

// Returns a pointer to an array of const char* and sets the count.
// The caller should not free the returned pointers.
extern "C" __declspec(dllexport) const char** GetComponentNames(int* count)
{
    static const char* names[] = {
        "MyComponent",
        "MyComponent2"
    };
    if (count) *count = sizeof(names) / sizeof(names[0]);
    return names;
}

GLuint m_shadowMapFBO = 0;
typedef int (*PFN_gladLoadGL)(void*);

//take in opengl context
extern "C" __declspec(dllexport) void SetOpenGLContext(HGLRC context, HDC hdc)
{
    wglMakeCurrent(hdc, context);
    gladLoaderLoadGL();
}