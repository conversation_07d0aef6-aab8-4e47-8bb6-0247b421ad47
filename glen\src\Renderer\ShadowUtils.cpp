#include "ShadowUtils.h"
#include "Components/Transform.h"
#include "Renderer/Renderer.h"
#include <algorithm>

ShadowUtils::FrustumCorners ShadowUtils::CalculateCameraFrustumCorners(Camera* camera, float nearPlane, float farPlane)
{
    FrustumCorners result;
    
    // Get camera transform
    auto transform = camera->GetGameObject()->GetTransform();
    glm::vec3 cameraPos = transform->GetPosition();
    glm::vec3 cameraFront = transform->GetForwardVector();
    glm::vec3 cameraUp = transform->GetUpVector();
    glm::vec3 cameraRight = transform->GetRightVector();
    
    // Calculate frustum dimensions
    float fovRad = glm::radians(camera->fov);
    float aspectRatio = camera->aspectRatio;
    
    // Near plane dimensions
    float nearHeight = 2.0f * nearPlane * tan(fovRad * 0.5f);
    float nearWidth = nearHeight * aspectRatio;
    
    // Far plane dimensions
    float farHeight = 2.0f * farPlane * tan(fovRad * 0.5f);
    float farWidth = farHeight * aspectRatio;
    
    // Calculate near plane center and corners
    glm::vec3 nearCenter = cameraPos + cameraFront * nearPlane;
    glm::vec3 farCenter = cameraPos + cameraFront * farPlane;
    
    // Near plane corners
    glm::vec3 nearTopLeft = nearCenter + cameraUp * (nearHeight * 0.5f) - cameraRight * (nearWidth * 0.5f);
    glm::vec3 nearTopRight = nearCenter + cameraUp * (nearHeight * 0.5f) + cameraRight * (nearWidth * 0.5f);
    glm::vec3 nearBottomLeft = nearCenter - cameraUp * (nearHeight * 0.5f) - cameraRight * (nearWidth * 0.5f);
    glm::vec3 nearBottomRight = nearCenter - cameraUp * (nearHeight * 0.5f) + cameraRight * (nearWidth * 0.5f);
    
    // Far plane corners
    glm::vec3 farTopLeft = farCenter + cameraUp * (farHeight * 0.5f) - cameraRight * (farWidth * 0.5f);
    glm::vec3 farTopRight = farCenter + cameraUp * (farHeight * 0.5f) + cameraRight * (farWidth * 0.5f);
    glm::vec3 farBottomLeft = farCenter - cameraUp * (farHeight * 0.5f) - cameraRight * (farWidth * 0.5f);
    glm::vec3 farBottomRight = farCenter - cameraUp * (farHeight * 0.5f) + cameraRight * (farWidth * 0.5f);
    
    // Store corners in consistent order
    result.corners.reserve(8);
    result.corners.push_back(nearTopLeft);
    result.corners.push_back(nearTopRight);
    result.corners.push_back(nearBottomLeft);
    result.corners.push_back(nearBottomRight);
    result.corners.push_back(farTopLeft);
    result.corners.push_back(farTopRight);
    result.corners.push_back(farBottomLeft);
    result.corners.push_back(farBottomRight);
    
    // Calculate center of frustum
    result.center = glm::vec3(0.0f);
    for (const auto& corner : result.corners)
    {
        result.center += corner;
    }
    result.center /= static_cast<float>(result.corners.size());
    
    return result;
}

glm::mat4 ShadowUtils::CalculateFittedLightViewMatrix(const glm::vec3& lightDirection, const FrustumCorners& frustum)
{
    // Normalize light direction (this should point FROM the light TO the scene)
    glm::vec3 lightDir = glm::normalize(lightDirection);

    // Position light far away in the OPPOSITE direction of lightDirection
    // If lightDirection points towards the scene, we want the light position to be behind it
    glm::vec3 lightPos = frustum.center - lightDir * 500.0f; // Increased distance for safety

    // Calculate up vector for light view matrix
    glm::vec3 up = glm::vec3(0.0f, 1.0f, 0.0f);
    if (abs(glm::dot(lightDir, up)) > 0.9f) // If light direction is too close to up vector
    {
        up = glm::vec3(1.0f, 0.0f, 0.0f); // Use right vector instead
    }

    return glm::lookAt(lightPos, frustum.center, up);
}

glm::mat4 ShadowUtils::CalculateFittedLightProjectionMatrix(const glm::mat4& lightView, const FrustumCorners& frustum)
{
    // Transform frustum corners to light space
    std::vector<glm::vec3> lightSpaceCorners = TransformToLightSpace(frustum.corners, lightView);

    // Calculate bounding box in light space
    glm::vec3 minBounds, maxBounds;
    CalculateLightSpaceBounds(lightSpaceCorners, minBounds, maxBounds);

    // Use very minimal padding for tight fitting - this will create more dramatic changes
    float padding = 0.5f; // Reduced padding for tighter fitting
    minBounds.x -= padding;
    minBounds.y -= padding;
    maxBounds.x += padding;
    maxBounds.y += padding;

    // Extend the depth range to capture shadow casters outside the frustum
    float zNear = minBounds.z - 20.0f; // Reduced extension for tighter fitting
    float zFar = maxBounds.z + 20.0f;

    // Ensure we have valid bounds
    if (zNear >= zFar) {
        zNear = -100.0f;
        zFar = 100.0f;
    }

    return glm::ortho(minBounds.x, maxBounds.x, minBounds.y, maxBounds.y, zNear, zFar);
}

glm::mat4 ShadowUtils::CalculateFittedLightSpaceMatrix(Camera* camera, const glm::vec3& lightDirection,
                                                       float nearPlane, float farPlane)
{
    // Get camera transform
    auto transform = camera->GetGameObject()->GetTransform();
    glm::vec3 cameraPos = transform->GetPosition();
    glm::vec3 cameraFront = transform->GetForwardVector();

    // Calculate focus point with stabilization to reduce swimming
    float focusDistance = nearPlane + (farPlane - nearPlane) * 0.2f; // Slightly further for stability
    glm::vec3 targetFocusPoint = cameraPos + cameraFront * focusDistance;

    // FOCUS POINT STABILIZATION: Smooth focus point changes to reduce swimming
    static glm::vec3 lastFocusPoint = targetFocusPoint;
    static bool firstFrame = true;

    if (firstFrame) {
        lastFocusPoint = targetFocusPoint;
        firstFrame = false;
    }

    // Heavy smoothing for focus point to reduce swimming when looking around
    glm::vec3 focusPoint = lastFocusPoint * 0.95f + targetFocusPoint * 0.05f;
    lastFocusPoint = focusPoint;

    // Calculate bounds of visible geometry within camera frustum
    glm::vec3 visibleBounds = CalculateVisibleGeometryBounds(camera, nearPlane, farPlane);

    // Use the maximum dimension of visible geometry for shadow sizing
    float maxDimension = std::max({visibleBounds.x, visibleBounds.y, visibleBounds.z * 0.3f});

    // Make shadow size responsive but with heavy stabilization
    float targetShadowSize = maxDimension * 0.7f;
    targetShadowSize = glm::max(targetShadowSize, 10.0f);   // Larger minimum for stability
    targetShadowSize = glm::min(targetShadowSize, 200.0f);  // Smaller maximum for stability

    // HYSTERESIS: Only change shadow size if the change is significant
    static float lastShadowSize = targetShadowSize;
    static float stableShadowSize = targetShadowSize;

    float sizeChange = abs(targetShadowSize - stableShadowSize);
    float changeThreshold = stableShadowSize * 0.3f; // 30% change required

    if (sizeChange > changeThreshold) {
        // Significant change detected, update stable size with heavy smoothing
        stableShadowSize = stableShadowSize * 0.95f + targetShadowSize * 0.05f;
    }

    // Apply additional smoothing to final shadow size
    float shadowSize = lastShadowSize * 0.98f + stableShadowSize * 0.02f;
    lastShadowSize = shadowSize;

    // Position light closer for tighter shadow coverage
    glm::vec3 lightPos = focusPoint - glm::normalize(lightDirection) * 50.0f;

    // Create light view matrix
    glm::vec3 up = glm::vec3(0.0f, 1.0f, 0.0f);
    if (abs(glm::dot(glm::normalize(lightDirection), up)) > 0.9f) {
        up = glm::vec3(1.0f, 0.0f, 0.0f);
    }
    glm::mat4 lightView = glm::lookAt(lightPos, focusPoint, up);

    // Create orthographic projection with closer near plane
    glm::mat4 lightProjection = glm::ortho(-shadowSize, shadowSize, -shadowSize, shadowSize, 0.1f, 100.0f);

    return lightProjection * lightView;
}

std::vector<glm::vec3> ShadowUtils::TransformToLightSpace(const std::vector<glm::vec3>& worldCorners, 
                                                          const glm::mat4& lightView)
{
    std::vector<glm::vec3> lightSpaceCorners;
    lightSpaceCorners.reserve(worldCorners.size());
    
    for (const auto& corner : worldCorners)
    {
        glm::vec4 lightSpaceCorner = lightView * glm::vec4(corner, 1.0f);
        lightSpaceCorners.push_back(glm::vec3(lightSpaceCorner));
    }
    
    return lightSpaceCorners;
}

void ShadowUtils::CalculateLightSpaceBounds(const std::vector<glm::vec3>& lightSpaceCorners,
                                            glm::vec3& minBounds, glm::vec3& maxBounds)
{
    if (lightSpaceCorners.empty()) return;

    minBounds = lightSpaceCorners[0];
    maxBounds = lightSpaceCorners[0];

    for (const auto& corner : lightSpaceCorners)
    {
        minBounds.x = std::min(minBounds.x, corner.x);
        minBounds.y = std::min(minBounds.y, corner.y);
        minBounds.z = std::min(minBounds.z, corner.z);

        maxBounds.x = std::max(maxBounds.x, corner.x);
        maxBounds.y = std::max(maxBounds.y, corner.y);
        maxBounds.z = std::max(maxBounds.z, corner.z);
    }
}

glm::vec3 ShadowUtils::CalculateVisibleGeometryBounds(Camera* camera, float nearPlane, float farPlane)
{
    // Calculate camera frustum
    FrustumCorners frustum = CalculateCameraFrustumCorners(camera, nearPlane, farPlane);

    // Get camera matrices for frustum culling
    auto transform = camera->GetGameObject()->GetTransform();
    glm::mat4 view = glm::lookAt(
        transform->GetPosition(),
        transform->GetPosition() + transform->GetForwardVector(),
        transform->GetUpVector()
    );
    glm::mat4 proj = glm::perspective(
        glm::radians(camera->fov),
        camera->aspectRatio,
        nearPlane,
        farPlane
    );
    glm::mat4 viewProj = proj * view;

    // Find bounds of all visible geometry
    glm::vec3 minBounds(FLT_MAX);
    glm::vec3 maxBounds(-FLT_MAX);
    bool foundAnyGeometry = false;

    // Access the render queue to get actual mesh bounds
    // Note: This requires access to Renderer's render queue
    // For now, we'll use a simplified approach based on frustum size

    // Calculate the size of the frustum at different depths
    float nearSize = 2.0f * nearPlane * tan(glm::radians(camera->fov) * 0.5f);
    float farSize = 2.0f * farPlane * tan(glm::radians(camera->fov) * 0.5f);
    float avgSize = (nearSize + farSize) * 0.5f;

    // Return a size that represents the visible area
    // This is a simplified version - ideally we'd check actual mesh bounds
    return glm::vec3(avgSize * camera->aspectRatio, avgSize, farPlane - nearPlane);
}
