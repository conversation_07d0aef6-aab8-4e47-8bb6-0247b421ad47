#pragma once
#include "Component.h"
#include "Transform.h"
#include "SoundManager.h"
#include <glm/glm.hpp> // Include glm header for vec4
#include <Renderer/Framebuffer.h>

typedef glm::vec3 Color;

class Camera : public Component
{
public:

    enum class CameraType
        {
            PERSPECTIVE,
            ORTHOGRAPHIC
        };
        
    Camera();
    ~Camera();

    void Update() override;
    void Start() override;
    virtual void Render();
    virtual void PreRender();
    virtual void PostRender();

    void SetCameraType(CameraType type) { m_CameraType = type; }
    CameraType GetCameraType() const { return m_CameraType; }

    void CalculateAspectRatio(float width, float height)
    {
        aspectRatio = width / height;
    }

    struct FrustumCorners
    {
        glm::vec3 nearTopLeft;
        glm::vec3 nearTopRight;
        glm::vec3 nearBottomLeft;
        glm::vec3 nearBottomRight;
        glm::vec3 farTopLeft;
        glm::vec3 farTopRight;
        glm::vec3 farBottomLeft;
        glm::vec3 farBottomRight;
        glm::vec3 center;
    };

    FrustumCorners GetFrustumCorners(float nearPlane, float farPlane)
    {
        FrustumCorners result;
        
        // Get camera transform
        auto transform = GetGameObject()->GetTransform();
        glm::vec3 cameraPos = transform->GetPosition();
        glm::vec3 cameraFront = transform->GetForwardVector();
        glm::vec3 cameraUp = transform->GetUpVector();
        glm::vec3 cameraRight = glm::normalize(glm::cross(cameraFront, cameraUp));

        // Calculate frustum dimensions
        float fovRad = glm::radians(fov);
        float aspectRatio = this->aspectRatio;
        
        // Near plane dimensions
        float nearHeight = 2.0f * nearPlane * tan(fovRad * 0.5f);
        float nearWidth = nearHeight * aspectRatio;
        
        // Far plane dimensions
        float farHeight = 2.0f * farPlane * tan(fovRad * 0.5f);

        float farWidth = farHeight * aspectRatio;
        
        // Calculate near plane center and corners
        glm::vec3 nearCenter = cameraPos + cameraFront * nearPlane;
        result.nearTopLeft = nearCenter + cameraUp * (nearHeight * 0.5f) - cameraRight * (nearWidth * 0.5f);

        result.nearTopRight = nearCenter + cameraUp * (nearHeight * 0.5f) + cameraRight * (nearWidth * 0.5f);
        result.nearBottomLeft = nearCenter - cameraUp * (nearHeight * 0.5f) - cameraRight * (nearWidth * 0.5f);

        result.nearBottomRight = nearCenter - cameraUp * (nearHeight * 0.5f) + cameraRight * (nearWidth * 0.5f);

        // Calculate far plane center and corners
        glm::vec3 farCenter = cameraPos + cameraFront * farPlane;

        result.farTopLeft = farCenter + cameraUp * (farHeight * 0.5f) - cameraRight * (farWidth * 0.5f);
        result.farTopRight = farCenter + cameraUp * (farHeight * 0.5f) + cameraRight * (farWidth * 0.5f);

        result.farBottomLeft = farCenter - cameraUp * (farHeight * 0.5f) - cameraRight * (farWidth * 0.5f);
        result.farBottomRight = farCenter - cameraUp * (farHeight * 0.5f) + cameraRight * (farWidth * 0.5f);

        // Calculate center of frustum
        result.center = glm::vec3(0.0f);

        result.center = (result.nearTopLeft + result.nearTopRight + result.nearBottomLeft + result.nearBottomRight + result.farTopLeft + result.farTopRight + result.farBottomLeft + result.farBottomRight) / 8.0f;

        return result;
    }

    std::shared_ptr<FrameBuffer> GetFrameBuffer(const std::string& name)
    {
        auto it = m_Framebuffers.find(name);
        if (it != m_Framebuffers.end())
        {
            return it->second;
        }
        return nullptr; // Return nullptr if framebuffer not found
    }
    void AddFrameBuffer(const std::string& name, std::shared_ptr<FrameBuffer> framebuffer)
    {
        m_Framebuffers[name] = framebuffer;
    }
    void RemoveFrameBuffer(const std::string& name)
    {
        m_Framebuffers.erase(name);
    }


    Color backgroundColor = Color(0.0f, 0.0f, 0.0f); // Default background color (black)
    float fov = 45.0f;                               // Field of view
    float nearClip = 0.1f;                           // Near clipping plane
    float farClip = 100.0f;                          // Far clipping plane
    float aspectRatio = 16.0f / 9.0f;                // Aspect ratio

    std::vector<BuiltInFieldInfo> GetBuiltInFields() override
    {
        std::vector<BuiltInFieldInfo> fields;
        fields.reserve(5);
        fields.push_back(BuiltInFieldInfo{"int", "Camera Type", &m_CameraType});
        fields.push_back(BuiltInFieldInfo{"float", "FOV", &fov});
        fields.push_back(BuiltInFieldInfo{"float", "NearClip", &nearClip});
        fields.push_back(BuiltInFieldInfo{"float", "FarClip", &farClip});
        fields.push_back(BuiltInFieldInfo{"vec3", "Background Color", &backgroundColor});
        

        return fields;
    }

    std::unique_ptr<FrameBuffer> m_pFrameBuffer; // Default framebuffer for the camera
    std::unique_ptr<FrameBuffer> m_pGizmoBuffer; // Gizmo framebuffer for the camera
    std::unique_ptr<FrameBuffer> m_pNormalBuffer; // Normal buffer for the camera
    std::unique_ptr<FrameBuffer> m_pDepthBuffer;  // Depth buffer for the camera
    std::unique_ptr<FrameBuffer> m_pHBAOBuffer;  // HBAO buffer for the camera

    float hbaoRadius = 0;
    float hbaoBias = 0;
    float hbaoIntensity = 0;
    float hbaoDirections = 0;
    float hbaoSteps = 0;

private:
    CameraType m_CameraType = CameraType::PERSPECTIVE; // Default camera type
    std::unordered_map<std::string, std::shared_ptr<FrameBuffer>> m_Framebuffers; // Framebuffers for rendering

};
