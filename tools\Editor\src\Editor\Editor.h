#pragma once
#include <memory>
#include <glen.h>

namespace Editor
{

    static bool bIsSceneEditorFocused;
    static bool bIsSceneEditorHovered;

    void InitializeEditor();
    void ShutdownEditor();

    void RenderEditor();
    void RenderScene();

    void Update();
    
    void SetupIMGUI();

    class EditorState
    {
    public:
        static EditorState& GetInstance()
        {
            static EditorState instance;
            return instance;
        }

        void SetActiveGameObject(std::shared_ptr<GameObject> gameObject) { m_ActiveGameObject = gameObject; }
        std::shared_ptr<GameObject> GetActiveGameObject() const { return m_ActiveGameObject; }
        int GetActiveGameObjectIndex() const
        {
            auto scene = GetActiveScene();
            if (scene)
            {
                auto gameObjects = scene->GetGameObjects();
                for (size_t i = 0; i < gameObjects.size(); ++i)
                {
                    if (gameObjects[i] == m_ActiveGameObject)
                    {
                        return static_cast<int>(i);
                    }
                }
            }
            return -1; // Not found
        }
        void SetActiveGameObject(int index)
        {
            auto scene = GetActiveScene();
            if (scene && index >= 0 && index < scene->GetGameObjects().size())
            {
                m_ActiveGameObject = scene->GetGameObjects()[index];
            }
            else
            {
                m_ActiveGameObject = nullptr;
            }
        }
        void SetActiveGameObjectByName(const std::string& name)
        {
            auto scene = GetActiveScene();
            if (scene)
            {
                m_ActiveGameObject = scene->GetGameObjectByName(name);
            }
            else
            {
                m_ActiveGameObject = nullptr;
            }
        }
        void SetActiveScene(std::shared_ptr<Scene> scene) { m_ActiveScene = scene; }
        std::shared_ptr<Scene> GetActiveScene() const { return m_ActiveScene; }
        std::shared_ptr<GameObject> GetEditorCamera() const { return m_EditorCameraObject; }
        int selectedGameObjectIndex = -1;

        bool bIsSceneEditorHovered = false;
        bool bIsSceneEditorFocused = false;
        

    private:
        std::shared_ptr<GameObject> m_ActiveGameObject;
        std::shared_ptr<Scene> m_ActiveScene;
        std::shared_ptr<GameObject> m_EditorCameraObject = nullptr;
        EditorState() : m_ActiveGameObject(nullptr), m_ActiveScene(nullptr), m_EditorCameraObject(std::make_shared<GameObject>()) {}

};

} // namespace Editor