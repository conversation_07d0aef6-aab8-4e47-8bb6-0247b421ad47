{"files.associations": {"cmath": "cpp", "unordered_map": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "*.tcc": "cpp", "cctype": "cpp", "charconv": "cpp", "chrono": "cpp", "clocale": "cpp", "compare": "cpp", "concepts": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "string": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "ratio": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "format": "cpp", "fstream": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "new": "cpp", "numbers": "cpp", "ostream": "cpp", "semaphore": "cpp", "span": "cpp", "sstream": "cpp", "stdexcept": "cpp", "stop_token": "cpp", "streambuf": "cpp", "text_encoding": "cpp", "thread": "cpp", "cinttypes": "cpp", "typeinfo": "cpp", "variant": "cpp", "forward_list": "cpp", "ios": "cpp", "list": "cpp", "locale": "cpp", "map": "cpp", "mutex": "cpp", "xfacet": "cpp", "xhash": "cpp", "xiosbase": "cpp", "xlocale": "cpp", "xlocbuf": "cpp", "xlocinfo": "cpp", "xlocmes": "cpp", "xlocmon": "cpp", "xlocnum": "cpp", "xloctime": "cpp", "xmemory": "cpp", "xstddef": "cpp", "xstring": "cpp", "xtr1common": "cpp", "xtree": "cpp", "xutility": "cpp", "condition_variable": "cpp", "codecvt": "cpp", "filesystem": "cpp", "queue": "cpp", "*.rh": "cpp", "bitset": "cpp", "complex": "cpp", "coroutine": "cpp", "csignal": "cpp", "future": "cpp", "unordered_set": "cpp"}, "C_Cpp.default.configurationProvider": "ms-vscode.cmake-tools"}