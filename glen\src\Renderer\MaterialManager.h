#include <unordered_map>
#include <memory>
#include <stdexcept>
#include "Material.h"

class MaterialManager
{
public:
    static void LoadMaterial(const std::string &name, const std::shared_ptr<Material> &material);
    static std::shared_ptr<Material> GetMaterial(const std::string &name);
    static void ClearMaterials();

private:
    static std::unordered_map<std::string, std::shared_ptr<Material>> materials;
};