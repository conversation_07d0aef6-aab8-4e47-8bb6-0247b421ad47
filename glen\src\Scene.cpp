#include "Scene.h"
#include <stdexcept>
#include <Renderer/Renderer.h>
#include <unordered_set>

std::shared_ptr<Scene> Scene::s_pActiveScene = nullptr;

Scene::Scene()
{
    m_szName = "DefaultScene";
    m_pGameObjects = std::vector<std::shared_ptr<GameObject>>();
}

Scene::~Scene()
{
    // Clean up game objects
    for (auto& go : m_pGameObjects)
    {
        go.reset(); // Reset shared_ptr to release the object
    }
    m_pGameObjects.clear(); // Clear the vector of game objects

    s_pActiveScene.reset(); // Reset the active scene pointer

}


void Scene::Update()
{
    // Update all game objects in the scene
    if (m_pGameObjects.empty()) return; // No game objects to update

    int nGameObjects = m_pGameObjects.size();
    
    for (int i = 0; i < nGameObjects; ++i)
    {
        if (m_pGameObjects[i])
        {
            m_pGameObjects[i]->Update();
        }
    }
    
}

void Scene::Start()
{
    for (auto const& go : m_pGameObjects)
    {
        go->Start();
    }
}

void Scene::SetName(const std::string& name)
{
    m_szName = name;
}

/// @brief Adds a game object to the scene with a unique name.
/// If a game object with the same name already exists, it appends a number to make it unique.
/// @param gameObject 
void Scene::AddGameObject(std::shared_ptr<GameObject> gameObject)
{
    if (!gameObject) return;

    std::string baseName = gameObject->GetName();
    std::string newName = baseName;
    int suffix = 1;

    // Collect all names that match the base name or base name + number
    std::unordered_set<std::string> existingNames;
    for (const auto& go : m_pGameObjects) {
        if (go) {
            existingNames.insert(go->GetName());
        }
    }

    // Find a unique name
    while (existingNames.count(newName)) {
        // If baseName already ends with a number, strip it for incrementing
        size_t pos = baseName.find_last_not_of("0123456789");
        if (pos != std::string::npos && pos + 1 < baseName.size()) {
            // baseName has a numeric suffix, strip it
            baseName = baseName.substr(0, pos + 1);
        }
        newName = baseName + std::to_string(suffix++);
    }

    gameObject->SetName(newName);
    m_pGameObjects.push_back(gameObject);
}

void Scene::RemoveGameObject(std::shared_ptr<GameObject> gameObject)
{
    auto it = std::remove(m_pGameObjects.begin(), m_pGameObjects.end(), gameObject);
    if (it != m_pGameObjects.end())
    {
        m_pGameObjects.erase(it, m_pGameObjects.end());
    }
}
std::vector<std::shared_ptr<GameObject>> Scene::GetGameObjects()
{
    return m_pGameObjects;
}

std::shared_ptr<GameObject> Scene::GetGameObjectByName(const std::string& name)
{
    for (auto const& go : m_pGameObjects)
    {
        if (go->GetName() == name)
        {
            return go;
        }
    }
    return nullptr;
}

void Scene::SetActiveScene(std::shared_ptr<Scene> scene)
{
    if (scene != nullptr)
    {
        s_pActiveScene = scene;
    }
    else
    {
        throw std::runtime_error("Cannot set active scene to null.");
    }
}

std::shared_ptr<Scene> Scene::GetActiveScene()
{
    return s_pActiveScene;
}

// Free function for adding a game object to the active scene
void AddGameObject(std::shared_ptr<GameObject> gameObject) {
    if (Scene::GetActiveScene()) {
        Scene::GetActiveScene()->AddGameObject(gameObject);
    } else {
        throw std::runtime_error("No active scene set. Cannot add game object.");
    }
}