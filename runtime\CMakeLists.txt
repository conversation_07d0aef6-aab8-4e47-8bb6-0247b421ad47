cmake_minimum_required(VERSION 3.25)

project(glenclient VERSION 0.0.1)

set(CMAKE_CXX_STANDARD 17)

# Set static linking flags for Windows (optional)
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -static-libgcc -static-libstdc++ -static")

# ========= Paths =========
set(SRC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../src)
set(RES_DIR ${CMAKE_CURRENT_SOURCE_DIR}/resources)
#set(OUT_DIR ${CMAKE_CURRENT_SOURCE_DIR}/bin/${CMAKE_BUILD_TYPE})
set(OUT_DIR ${CMAKE_CURRENT_SOURCE_DIR}/bin)

# ========= Editor Executable =========
add_executable(${PROJECT_NAME}
    main.cpp
)

target_include_directories(${PROJECT_NAME} PRIVATE ${SRC_DIR})
target_link_libraries(${PROJECT_NAME} PRIVATE glen)

# Output directory for editor
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${OUT_DIR}
)

# ========= Copy Resources =========
add_custom_command(
    TARGET ${PROJECT_NAME} POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_directory
            "${RES_DIR}" "${OUT_DIR}/resources"
    COMMENT "Found new resources, copying to output directory..."
)


if (WIN32)
    set_target_properties(${PROJECT_NAME} PROPERTIES
        WIN32_EXECUTABLE $<IF:$<CONFIG:Release,MinSizeRel>,ON,OFF>
    )
endif ()

#if msvc /SUBSYSTEM:CONSOLE
if (MSVC)
    set_target_properties(${PROJECT_NAME} PROPERTIES
        LINK_FLAGS "/SUBSYSTEM:CONSOLE"
    )
endif ()