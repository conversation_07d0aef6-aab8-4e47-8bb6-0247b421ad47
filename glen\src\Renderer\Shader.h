#pragma once

#include "ShaderManager.h"
#include <glad/gl.h>
#include <vector>
#include <iostream>
#include <glm/glm.hpp>
#include <glm/gtc/type_ptr.hpp>

class ShaderAttribute
{
    public:
    int attributeID;
    std::string attributeName;
    uint32_t attributeType;
};

class ShaderUniform
{
    public:
    int uniformID;
    std::string uniformName;
    uint32_t uniformType;
    GLint uniformSize;
    GLint uniformLocation;
    union
    {
        bool boolValue;
        int32_t intValue;
        float floatValue;
        glm::vec2 vec2Value;
        glm::vec3 vec3Value;
        glm::vec4 vec4Value;
        glm::mat4 mat4Value;
    } uniformValue;
};

class Shader
{
public:
    Shader(const std::string &name);
    Shader ();

    ~Shader();

    Shader& operator=(const Shader& other)
    {
        m_nID = other.m_nID;
        m_szName = other.m_szName;
        return *this;
    }

    void SetVec4(glm::vec4 value, const std::string &name);

    void SetMat4(glm::mat4 value, const std::string &name);

    void SetInt(int value, const std::string &name);

    void SetFloat(float value, const std::string &name);

    void SetVec3(glm::vec3 value, const std::string &name);

    void SetVec2(glm::vec2 &value, const std::string &name);

    void SetBool(bool value, const std::string &name);

    void Bind();

    void Unbind();

    void Load(const std::string &vertexPath, const std::string &fragmentPath, const std::string &geometryPath);

    void Compile();

    void SetName(const std::string &name);

    GLuint GetShaderID();
    std::string GetName();

    std::vector<ShaderUniform> GetActiveUniforms();
    std::vector<ShaderAttribute> GetActiveAttributes();

    void PrintUniforms();

private:
    GLuint m_nID;
    std::string m_szName;
};