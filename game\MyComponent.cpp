// MyComponent.cpp

#include "MyComponent.h"
#include <glen.h>
#include <iostream>

Camera *camera = nullptr;

void MyComponent::Start()
{
    camera = go->GetComponent<Camera>().get();
}

void MyComponent::Update()
{
    if(!test)
    {
        return; // Skip update if test is false
    }
    float cameraAccelerationFactor = 1.0f;

    if (input->IsKeyDown(GLFW_KEY_LEFT_SHIFT))
    {
        cameraAccelerationFactor = 3.0f;
    }

    float cameraSpeed = (cameraAccelerationFactor * 2.5f) * time->GetDelta();

    glm::vec3 pos = go->GetTransform()->GetPosition();
    auto rot = go->GetTransform()->GetRotation();

    float yaw = rot.y;
    float pitch = rot.x;
    static float lastX = 0.0f;
    static float lastY = 0.0f;
    static bool firstMouse = true;

    if (input->IsMouseButtonDown(GLFW_MOUSE_BUTTON_RIGHT))
    {
        input->EnableCursor(true);
        double xpos, ypos;
        auto mousePos = input->GetMousePosition();

        xpos = mousePos.x;
        ypos = mousePos.y;

        if (firstMouse)
        {
            lastX = xpos;
            lastY = ypos;
            firstMouse = false;
        }

        float xoffset = lastX - xpos;
        float yoffset = lastY - ypos;
        lastX = xpos;
        lastY = ypos;

        float sensitivity = 0.1f;
        float deltaTime = time->GetDelta();
        xoffset *= sensitivity * deltaTime;
        yoffset *= sensitivity * deltaTime;

        yaw += xoffset;
        pitch += yoffset;

        // Clamp the pitch to prevent flipping
        pitch = ClampRadiansPitch(glm::vec3(pitch, yaw, 0.0f), -89.0f, 89.0f);

        // Apply normalized front vector to the camera
        go->GetTransform()->SetRotation(glm::vec3(pitch, yaw, 0.0f));
    }
    else
    {
        firstMouse = true; // Reset the flag when the right mouse button is released
        input->EnableCursor(false);
    }

    if (input->IsKeyDown(GLFW_KEY_W))
    {
        pos += cameraSpeed * go->GetTransform()->GetForwardVector();
    }
    if (input->IsKeyDown(GLFW_KEY_S))
    {
        pos -= cameraSpeed * go->GetTransform()->GetForwardVector();
    }

    if (input->IsKeyDown(GLFW_KEY_A))
    {
        pos -= cameraSpeed * go->GetTransform()->GetRightVector();
    }
    if (input->IsKeyDown(GLFW_KEY_D))
    {
        pos += cameraSpeed * go->GetTransform()->GetRightVector();
    }

    if (input->IsKeyDown(GLFW_KEY_E))
    {
        pos += cameraSpeed * glm::vec3(0.0f, 1.0f, 0.0f); // Move up along the global Y-axis
    }

    if (input->IsKeyDown(GLFW_KEY_Q))
    {
        pos -= cameraSpeed * glm::vec3(0.0f, 1.0f, 0.0f); // Move down along the global Y-axis
    }

    if(input->IsKeyPressed(GLFW_KEY_4))
    {
        auto testObject = std::make_shared<GameObject>("TestObjectFromMyComponent");
        auto light = std::make_shared<Light>();
        testObject->AddComponent(light);
        light->m_Type = Light::LightType::POINT;
        light->m_bShadowEnabled = false;
        light->SetColor(glm::vec3(0.2f, 0.8f, 0.9f));
        light->go->GetTransform()->SetPosition(pos);

        scene->AddGameObject(testObject);
        testObject->Start();

    }

    // Set the new position of the GameObject
    go->GetTransform()->SetPosition(pos);
}

const char *MyComponent::GetTypeName() const
{
    return "MyComponent";
}

void MyComponent::SetGameObject(GameObject *obj)
{
    std::cout << "GameObject name: " << obj->GetName() << "\n";
    go = obj;
}


std::vector<BuiltInFieldInfo> MyComponent::GetBuiltInFields()
{
    std::vector<BuiltInFieldInfo> fields;
    fields.reserve(1);
    fields.push_back(BuiltInFieldInfo{"bool", "test", &test});
    return fields;
}