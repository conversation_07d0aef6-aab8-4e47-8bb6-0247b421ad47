#include "Editor.h"
#include "EditorGizmos.h"

#include <imgui.h>
#include <imgui_internal.h>
#include <imgui_impl_glfw.h>
#include <imgui_impl_opengl3.h>

void Editor::InitializeEditor()
{
    SetupIMGUI();

    EditorState::GetInstance().SetActiveGameObject(nullptr);
    EditorState::GetInstance().SetActiveScene(nullptr);

    // Initialize editor gizmo system
    EditorGizmos::Initialize();

    // Set up gizmo rendering callback so gizmos render at the right time
    Renderer::SetGizmoRenderCallback([](Camera* camera) {
        EditorGizmos::RenderGizmos(camera);
    });

    // Initialize editor camera
    auto editorCamera = std::make_shared<Camera>();
    editorCamera->SetCameraType(Camera::CameraType::PERSPECTIVE);
    editorCamera->fov = 45.0f;
    editorCamera->nearClip = 0.1f;
    editorCamera->farClip = 100.0f;
    editorCamera->backgroundColor = Color(0.1f, 0.1f, 0.1f); // Set a default background color
    EditorState::GetInstance().GetEditorCamera()->AddComponent(editorCamera);

    EditorState::GetInstance().GetEditorCamera()->GetTransform()->SetPosition(glm::vec3(0.0f, 5.0f, 10.0f));
    EditorState::GetInstance().GetEditorCamera()->GetTransform()->Rotate(glm::vec3(-0.0f, 0.0f, 0.0f)); // Set a default rotation for the editor camera

    // auto editorSceneFrameBuffer = std::make_shared<FrameBuffer>(100,100, FrameBuffer::FrameBufferType::COLOR);
    // auto editorSceneGizmoFrameBuffer = std::make_shared<FrameBuffer>(100,100, FrameBuffer::FrameBufferType::COLOR);

    // editorCamera->AddFrameBuffer("EditorScene", editorSceneFrameBuffer);
    // editorCamera->AddFrameBuffer("EditorSceneGizmo", editorSceneGizmoFrameBuffer);

    auto editorCameraComponent = EditorState::GetInstance().GetEditorCamera()->GetComponent<Camera>();
    editorCameraComponent->m_pFrameBuffer = std::make_unique<FrameBuffer>(1280, 720, FrameBuffer::FrameBufferType::COLOR);
    editorCameraComponent->m_pGizmoBuffer = std::make_unique<FrameBuffer>(1280, 720, FrameBuffer::FrameBufferType::COLOR);
    editorCameraComponent->m_pNormalBuffer = std::make_unique<FrameBuffer>(1280, 720, FrameBuffer::FrameBufferType::COLOR);
    editorCameraComponent->m_pDepthBuffer = std::make_unique<FrameBuffer>(1280, 720, FrameBuffer::FrameBufferType::COLOR);
    editorCameraComponent->m_pHBAOBuffer = std::make_unique<FrameBuffer>(1280, 720, FrameBuffer::FrameBufferType::COLOR);

    //load the sky shader
    auto skyShader = Renderer::GetShaderManager()->GetShader("sky");
    if (!skyShader)
    {
        //skyShader = std::make_shared<Shader>("skybox", "shaders/skybox.vert", "shaders/skybox.frag");
        Renderer::GetShaderManager()->LoadShader("sky");
    }

    //load gizmo billboard textures
    Renderer::GetTextureManager()->LoadTexture("resources\\editor\\icons\\audio.png");
    Renderer::GetTextureManager()->LoadTexture("resources\\editor\\icons\\gameobject.png");
    Renderer::GetTextureManager()->LoadTexture("resources\\editor\\icons\\light.png");
}

void Editor::ShutdownEditor()
{
    // Shutdown editor gizmo system
    EditorGizmos::Shutdown();
    EditorState::GetInstance().SetActiveGameObject(nullptr);
    EditorState::GetInstance().SetActiveScene(nullptr);
}

void Editor::RenderEditor()
{
    
}

void Editor::RenderScene()
{
    // Render the active scene
    auto activeScene = EditorState::GetInstance().GetActiveScene();


}

void Editor::Update()
{

    // Update the editor camera
    auto editorCamera = EditorState::GetInstance().GetEditorCamera();

    float cameraAccelerationFactor = 1.0f;
    InputManager input = InputManager::GetInstance();

    if (input.IsKeyDown(GLFW_KEY_LEFT_SHIFT))
    {
        cameraAccelerationFactor = 3.0f;
    }

    float cameraSpeed = (cameraAccelerationFactor * 2.5f) * Time::GetDeltaTime();

    glm::vec3 pos = editorCamera->GetTransform()->GetPosition();
    auto rot = editorCamera->GetTransform()->GetRotation();

    static float lastX = 0.0f;
    static float lastY = 0.0f;
    static bool firstMouse = true;
    static bool wasRightMouseDown = false;
    static bool isDraggingScene = false; // Track if we're dragging the scene

    bool rightMouseDown = input.IsMouseButtonDown(GLFW_MOUSE_BUTTON_RIGHT);
    bool sceneHovered = EditorState::GetInstance().bIsSceneEditorHovered;

    // Start drag only if hovered
    if (rightMouseDown && !wasRightMouseDown && sceneHovered)
    {
        isDraggingScene = true;
        InputManager::GetInstance().EnableCursor(false);
        auto mousePos = input.GetMousePosition();
        lastX = mousePos.x;
        lastY = mousePos.y;
        firstMouse = false;
    }
    // End drag on release
    else if (!rightMouseDown && wasRightMouseDown)
    {
        isDraggingScene = false;
        InputManager::GetInstance().EnableCursor(true);
        firstMouse = true;
    }

    if (isDraggingScene)
    {
        double xpos, ypos;
        auto mousePos = input.GetMousePosition();
        xpos = mousePos.x;
        ypos = mousePos.y;

        // Invert xoffset for natural camera look (left = left, right = right)
        float xoffset = (xpos - lastX) * 0.05f * Time::GetDeltaTime(); // Lower sensitivity
        float yoffset = (lastY - ypos) * 0.05f * Time::GetDeltaTime(); // Invert Y for typical FPS feel

        lastX = xpos;
        lastY = ypos;

        float yaw = rot.y - xoffset;
        float pitch = rot.x + yoffset;

        // Clamp the pitch to prevent flipping
        pitch = ClampRadiansPitch(glm::vec3(pitch, yaw, 0.0f), -89.0f, 89.0f);

        // Apply normalized front vector to the camera
        editorCamera->GetTransform()->SetRotation(glm::vec3(pitch, yaw, 0.0f));
    }

    wasRightMouseDown = rightMouseDown;

    // Camera movement
    if (input.IsKeyDown(GLFW_KEY_W))
    {
        pos += cameraSpeed * editorCamera->GetTransform()->GetForwardVector();
    }
    if (input.IsKeyDown(GLFW_KEY_S))
    {
        pos -= cameraSpeed * editorCamera->GetTransform()->GetForwardVector();
    }
    if (input.IsKeyDown(GLFW_KEY_A))
    {
        pos -= cameraSpeed * editorCamera->GetTransform()->GetRightVector();
    }
    if (input.IsKeyDown(GLFW_KEY_D))
    {
        pos += cameraSpeed * editorCamera->GetTransform()->GetRightVector();
    }
    if (input.IsKeyDown(GLFW_KEY_E))
    {
        pos += cameraSpeed * glm::vec3(0.0f, 1.0f, 0.0f); // Move up along the global Y-axis
    }
    if (input.IsKeyDown(GLFW_KEY_Q))
    {
        pos -= cameraSpeed * glm::vec3(0.0f, 1.0f, 0.0f); // Move down along the global Y-axis
    }

    // Set the new position of the GameObject
    editorCamera->GetTransform()->SetPosition(pos);

    editorCamera->Update();
}

void Editor::SetupIMGUI()
{
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGui_ImplGlfw_InitForOpenGL(Renderer::GetWindowPtr(), true);
    ImGui_ImplOpenGL3_Init("#version 450 core");
    ImGui::StyleColorsClassic();

    // setup imgui for high DPI
    ImGui::GetStyle().ScaleAllSizes(Renderer::GetWindowsDPI());

    // scale the fonts for high DPI
    ImGuiIO &io = ImGui::GetIO();

    // reload font with new scale
    io.Fonts->Clear();

    ImFontConfig font_cfg;
    font_cfg.FontDataOwnedByAtlas = false;
    font_cfg.OversampleH = 3; // Horizontal oversampling
    font_cfg.OversampleV = 1; // Vertical oversampling
    font_cfg.PixelSnapH = false;

    io.Fonts->AddFontFromFileTTF("resources/fonts/FiraCodeNerdFont-SemiBold.ttf", 16.0f * Renderer::GetWindowsDPI(), &font_cfg);

    io.Fonts->Build();

    // enable docking
    io.ConfigFlags |= ImGuiConfigFlags_DockingEnable;
}
