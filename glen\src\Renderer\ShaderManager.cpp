#include "ShaderManager.h"
#include <types.h>

ShaderManager::ShaderManager()
{
    // Load default shader in case a shader is not found while trying to render a material
    
    switch(LoadShader("default"))
    {
        case ReturnTypes::GENERALERROR:
            throw std::runtime_error("ERROR::SHADER::LOAD_FAILED");
            break;
        case ReturnTypes::SUCCESS:
            break;
        case ReturnTypes::SHADER_EXISTS:  
            break;
        case ReturnTypes::SHADER_NOT_FOUND:
            break;
        case ReturnTypes::SHADER_COMPILED:
            break;
    }

    // Load framebuffer shader
    switch(LoadShader("framebuffer"))
    {
        case ReturnTypes::GENERALERROR:
            throw std::runtime_error("ERROR::SHADER::LOAD_FAILED");
            break;
        case ReturnTypes::SUCCESS:
            break;
        case ReturnTypes::SHADER_EXISTS:  
            break;
        case ReturnTypes::SHADER_NOT_FOUND:
            break;
        case ReturnTypes::SHADER_COMPILED:
            break;
    }

    // Load shadow shader
    switch(<PERSON><PERSON><PERSON><PERSON><PERSON>("depth"))
    {
        case ReturnTypes::GENERALERROR:
            throw std::runtime_error("ERROR::SHADER::LOAD_FAILED");
            break;
        case ReturnTypes::SUCCESS:
            break;
        case ReturnTypes::SHADER_EXISTS:  
            break;
        case ReturnTypes::SHADER_NOT_FOUND:
            break;
        case ReturnTypes::SHADER_COMPILED:
            break;
    }

    switch(LoadShader("gaussian"))
    {
        case ReturnTypes::GENERALERROR:
            throw std::runtime_error("ERROR::SHADER::LOAD_FAILED");
            break;
        case ReturnTypes::SUCCESS:
            break;
        case ReturnTypes::SHADER_EXISTS:  
            break;
        case ReturnTypes::SHADER_NOT_FOUND:
            break;
        case ReturnTypes::SHADER_COMPILED:
            break;
    }
    
    switch(LoadShader("point_depth", true))
    {
        case ReturnTypes::GENERALERROR:
            throw std::runtime_error("ERROR::SHADER::LOAD_FAILED");
            break;
        case ReturnTypes::SUCCESS:
            break;
        case ReturnTypes::SHADER_EXISTS:  
            break;
        case ReturnTypes::SHADER_NOT_FOUND:
            break;
        case ReturnTypes::SHADER_COMPILED:
            break;
    }

    switch(LoadShader("gizmos", true))
    {
        case ReturnTypes::GENERALERROR:
            throw std::runtime_error("ERROR::SHADER::LOAD_FAILED");
            break;
        case ReturnTypes::SUCCESS:
            break;
        case ReturnTypes::SHADER_EXISTS:  
            break;
        case ReturnTypes::SHADER_NOT_FOUND:
            break;
        case ReturnTypes::SHADER_COMPILED:
            break;
    }

    switch(LoadShader("depth_to_color", true))
    {
        case ReturnTypes::GENERALERROR:
            throw std::runtime_error("ERROR::SHADER::LOAD_FAILED");
            break;
        case ReturnTypes::SUCCESS:
            break;
        case ReturnTypes::SHADER_EXISTS:  
            break;
        case ReturnTypes::SHADER_NOT_FOUND:
            break;
        case ReturnTypes::SHADER_COMPILED:
            break;
    }
    switch(LoadShader("vsm_depth", true))
    {
        case ReturnTypes::GENERALERROR:
            throw std::runtime_error("ERROR::SHADER::LOAD_FAILED");
            break;
        case ReturnTypes::SUCCESS:
            break;
        case ReturnTypes::SHADER_EXISTS:  
            break;
        case ReturnTypes::SHADER_NOT_FOUND:
            break;
        case ReturnTypes::SHADER_COMPILED:
            break;
    }
    switch(LoadShader("vsm_point_depth", true))
    {
        case ReturnTypes::GENERALERROR:
            throw std::runtime_error("ERROR::SHADER::LOAD_FAILED");
            break;
        case ReturnTypes::SUCCESS:
            break;
        case ReturnTypes::SHADER_EXISTS:  
            break;
        case ReturnTypes::SHADER_NOT_FOUND:
            break;
        case ReturnTypes::SHADER_COMPILED:
            break;
    }

    switch(LoadShader("bilateral_vsm", true))
    {
        case ReturnTypes::GENERALERROR:
            throw std::runtime_error("ERROR::SHADER::LOAD_FAILED");
            break;
        case ReturnTypes::SUCCESS:
            break;
        case ReturnTypes::SHADER_EXISTS:
            break;
        case ReturnTypes::SHADER_NOT_FOUND:
            break;
        case ReturnTypes::SHADER_COMPILED:
            break;
    }

    switch(LoadShader("billboard"))
    {
        case ReturnTypes::GENERALERROR:
            throw std::runtime_error("ERROR::SHADER::LOAD_FAILED");
            break;
        case ReturnTypes::SUCCESS:
            break;
        case ReturnTypes::SHADER_EXISTS:
            break;
        case ReturnTypes::SHADER_NOT_FOUND:
            break;
        case ReturnTypes::SHADER_COMPILED:
            break;
    }

    switch(LoadShader("sky"))
    {
        case ReturnTypes::GENERALERROR:
            throw std::runtime_error("ERROR::SHADER::LOAD_FAILED");
            break;
        case ReturnTypes::SUCCESS:
            break;
        case ReturnTypes::SHADER_EXISTS:
            break;
        case ReturnTypes::SHADER_NOT_FOUND:
            break;
        case ReturnTypes::SHADER_COMPILED:
            break;
    }

    switch(LoadShader("normal"))
    {
        case ReturnTypes::GENERALERROR:
            throw std::runtime_error("ERROR::SHADER::LOAD_FAILED");
            break;
        case ReturnTypes::SUCCESS:
            break;
        case ReturnTypes::SHADER_EXISTS:
            break;
        case ReturnTypes::SHADER_NOT_FOUND:
            break;
        case ReturnTypes::SHADER_COMPILED:
            break;
    }
    switch(LoadShader("depth_camera"))
    {
        case ReturnTypes::GENERALERROR:
            throw std::runtime_error("ERROR::SHADER::LOAD_FAILED");
            break;
        case ReturnTypes::SUCCESS:
            break;
        case ReturnTypes::SHADER_EXISTS:
            break;
        case ReturnTypes::SHADER_NOT_FOUND:
            break;
        case ReturnTypes::SHADER_COMPILED:
            break;
    }

    switch(LoadShader("hbao"))
    {
        case ReturnTypes::GENERALERROR:
            throw std::runtime_error("ERROR::SHADER::LOAD_FAILED");
            break;
        case ReturnTypes::SUCCESS:
            break;
        case ReturnTypes::SHADER_EXISTS:
            break;
        case ReturnTypes::SHADER_NOT_FOUND:
            break;
        case ReturnTypes::SHADER_COMPILED:
            break;
    }

    switch(LoadShader("infinite_grid"))
    {
        case ReturnTypes::GENERALERROR:
            throw std::runtime_error("ERROR::SHADER::LOAD_FAILED");
            break;
        case ReturnTypes::SUCCESS:
            break;
        case ReturnTypes::SHADER_EXISTS:
            break;
        case ReturnTypes::SHADER_NOT_FOUND:
            break;
        case ReturnTypes::SHADER_COMPILED:
            break;
    }

}

ShaderManager::~ShaderManager()
{
    m_pShaders.clear();
}

ReturnTypes ShaderManager::LoadShader(const std::string &shaderName, bool isGeometry)
{
    try
    {
        std::shared_ptr<Shader> shader = std::make_shared<Shader>(shaderName);

        shader.get()->Load("resources/shaders/" + shaderName + ".vert", 
            "resources/shaders/" + shaderName + ".frag", 
            isGeometry ? "resources/shaders/" + shaderName + ".geo" : "");

        m_pShaders.insert({shaderName, shader});

        return ReturnTypes::SUCCESS;
    }
    catch (const std::exception &e)
    {
        std::cerr << "ERROR::SHADER::LOAD_FAILED: " << e.what() << std::endl;
        return ReturnTypes::GENERALERROR;
    }
}

ReturnTypes ShaderManager::DeleteShader(const std::string &shaderName)
{
    if (m_pShaders.find(shaderName) != m_pShaders.end())
    {
        m_pShaders.erase(shaderName);
    }
    else
    {
        throw std::runtime_error("ERROR::SHADER::SHADER_NOT_FOUND");
        return ReturnTypes::SHADER_NOT_FOUND;
    }

    return ReturnTypes::SUCCESS;
}

std::shared_ptr<Shader> ShaderManager::GetShader(const std::string &shaderName)
{
    if (m_pShaders.find(shaderName) != m_pShaders.end())
    {
        return m_pShaders[shaderName];
    }
    else
    {
        return nullptr; // Shader not found
    }
}