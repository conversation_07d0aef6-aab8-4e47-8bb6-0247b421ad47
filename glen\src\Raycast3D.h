#pragma once

#include <glad/gl.h>
#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <glm/gtc/type_ptr.hpp>
#include <glm/gtx/quaternion.hpp>
#include <cmath>
#include <vector>

class Raycast3D
{
public:
    static bool RayIntersectsSphere(
        const glm::vec3 &orig,
        const glm::vec3 &dir,
        const glm::vec3 &center,
        float radius,
        float &t)
    {
        glm::vec3 L = center - orig;
        float tca = glm::dot(L, dir);
        float d2 = glm::dot(L, L) - tca * tca;
        if (d2 > radius * radius)
            return false;
        float thc = sqrt(radius * radius - d2);
        t = tca - thc;
        return true;
    }

    static bool RayIntersectsAABB(
        const glm::vec3 &orig,
        const glm::vec3 &dir,
        const glm::vec3 &min,
        const glm::vec3 &max,
        float &t)
    {
        float tmin = (min.x - orig.x) / dir.x;
        float tmax = (max.x - orig.x) / dir.x;

        if (tmin > tmax) std::swap(tmin, tmax);

        float tymin = (min.y - orig.y) / dir.y;
        float tymax = (max.y - orig.y) / dir.y;

        if (tymin > tymax) std::swap(tymin, tymax);

        if ((tmin > tymax) || (tymin > tmax))
            return false;

        if (tymin > tmin)
            tmin = tymin;

        if (tymax < tmax)
            tmax = tymax;

        if (tmin < 0)
            return false;

        t = tmin;
        return true;
    }

    static bool RayIntersectsTriangle(
        const glm::vec3 &orig,
        const glm::vec3 &dir,
        const glm::vec3 &v0,
        const glm::vec3 &v1,
        const glm::vec3 &v2,
        float &t)
    {
        const float EPSILON = 1e-6f;
        glm::vec3 edge1 = v1 - v0;
        glm::vec3 edge2 = v2 - v0;
        glm::vec3 h = glm::cross(dir, edge2);
        float a = glm::dot(edge1, h);
        if (fabs(a) < EPSILON)
            return false; // Ray is parallel to triangle
        float f = 1.0f / a;
        glm::vec3 s = orig - v0;
        float u = f * glm::dot(s, h);
        if (u < 0.0f || u > 1.0f)
            return false;
        glm::vec3 q = glm::cross(s, edge1);
        float v = f * glm::dot(dir, q);
        if (v < 0.0f || u + v > 1.0f)
            return false;
        t = f * glm::dot(edge2, q);
        if (t > EPSILON)
            return true;
        return false;
    }
    
};