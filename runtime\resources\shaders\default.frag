#version 450

in VS_OUT {
    vec3 FragPos;
    vec3 Normal;
    vec2 TexCoords;
    vec4 FragPosLightSpace;
    vec3 fragNormal;
    vec3 fragTangent;
    vec4 fragColor;
} fs_in;

layout (location = 0) out vec4 outColor;

//POINT LIGHT STUFF
#define MAX_LIGHTS 10

struct Light
{
    vec3 position;
    vec3 color;
    float intensity;
    float radius;
    int type; // 0: Directional, 1: Point
    float shadowBias; // Shadow bias for this light
    bool shadowEnabled;
};

uniform float far_plane; // Add this uniform for point light shadow


uniform sampler2D albedo;
uniform sampler2D shadowMap; // Blurred shadow map
uniform sampler2D normalMap;
uniform samplerCube pointShadowMap[MAX_LIGHTS];

uniform vec4 mainColor;
uniform vec3 ambientLight; // Ambient light color
uniform bool discardTransparent; // Control whether to discard transparent fragments
uniform vec3 mainDirectionalLightDirection; // Directional Light direction
uniform vec3 mainDirectionalLightColor; // Directional Light color
uniform float mainDirectionalLightIntensity; // Directional Light intensity
uniform vec3 viewPos; // View position
uniform float shadowBias; // Shadow bias

//POINT LIGHT STUFF
uniform Light lights[MAX_LIGHTS];
uniform int lightCount; // Number of lights in the scene

const float weights[5] = float[](0.227027, 0.1945946, 0.1216216, 0.054054, 0.016216);

float rand(vec2 co) {
    return fract(sin(dot(co.xy ,vec2(12.9898,78.233))) * 43758.5453);
}

float DirectionalLightShadowCalculationBlur(vec4 fragPosLightSpace)
{
    // Perform perspective divide
    vec3 projCoords = fragPosLightSpace.xyz / fragPosLightSpace.w;
    // Transform to [0,1] range
    projCoords = projCoords * 0.5 + 0.5;
    // Get depth of current fragment from light's perspective
    float currentDepth = projCoords.z;

    // Gaussian blur
    float shadow = 0.0;
    vec2 texelSize = 1.0 / textureSize(shadowMap, 0);
    for (int i = -4; i <= 4; ++i)
    {
        for (int j = -4; j <= 4; ++j)
        {
            float weight = weights[abs(i)] * weights[abs(j)];
            float pcfDepth = texture(shadowMap, projCoords.xy + vec2(i, j) * texelSize).r;
            shadow += weight * (currentDepth > pcfDepth + shadowBias ? 1.0 : 0.0);
        }
    }
    
    float jitterAmount = 0.35; // Tweak for softness
    vec2 jitter = (rand(gl_FragCoord.xy) - 0.5) * jitterAmount * texelSize;

    for (int i = -4; i <= 4; ++i)
    {
        for (int j = -4; j <= 4; ++j)
        {
            float weight = weights[abs(i)] * weights[abs(j)];
            vec2 offset = vec2(i, j) * texelSize + jitter;
            float pcfDepth = texture(shadowMap, projCoords.xy + offset).r;
            shadow += weight * (currentDepth > pcfDepth + shadowBias ? 1.0 : 0.0);
        }
    }

    

    return shadow;
}

float VSMDirectionalShadow(vec4 fragPosLightSpace)
{
    // Project to light space
    vec3 projCoords = fragPosLightSpace.xyz / fragPosLightSpace.w;
    projCoords = projCoords * 0.5 + 0.5;

    // Sample the VSM shadow map (RG32F format)
    vec2 moments = texture(shadowMap, projCoords.xy).rg;
    float depth = projCoords.z;

    // Basic depth test
    if (depth <= moments.x) {
        return 1.0; // Fully lit
    }

    // Calculate variance
    float variance = moments.y - (moments.x * moments.x);
    variance = max(variance, 0.00002); // Minimum variance to avoid artifacts

    // Calculate probability using Chebyshev's inequality
    float d = depth - moments.x;
    float p_max = variance / (variance + d * d);

    // Light bleeding reduction
    float amount = 0.3; // Adjust this value (0.0 to 1.0)
    p_max = clamp((p_max - amount) / (1.0 - amount), 0.0, 1.0);

    return p_max;
}


// VSM Point light shadow calculation using cubemap
float VSMPointShadowCalculation(vec3 fragPos, int lightIndex, out float outFade)
{
    vec3 fragToLight = fragPos - lights[lightIndex].position;
    float currentDepth = length(fragToLight);

    // Normalize depth by light radius (same as in VSM depth shader)
    float normalizedDepth = currentDepth / lights[lightIndex].radius;

    // Multi-sample VSM for smoother shadows
    vec2 moments = vec2(0.0);
    float sampleCount = 0.0;

    // Sample multiple points around the direction for smoother shadows
    vec3 sampleOffsets[4] = vec3[](
        vec3(0.01, 0.0, 0.0),
        vec3(-0.01, 0.0, 0.0),
        vec3(0.0, 0.01, 0.0),
        vec3(0.0, -0.01, 0.0)
    );

    // Main sample
    moments += texture(pointShadowMap[lightIndex], fragToLight).rg;
    sampleCount += 1.0;

    // Additional samples for smoothing
    for(int i = 0; i < 4; i++) {
        vec3 sampleDir = normalize(fragToLight + sampleOffsets[i]);
        moments += texture(pointShadowMap[lightIndex], sampleDir * length(fragToLight)).rg;
        sampleCount += 1.0;
    }

    moments /= sampleCount;

    // Basic depth test
    if (normalizedDepth <= moments.x) {
        outFade = smoothstep(lights[lightIndex].radius * 0.65, lights[lightIndex].radius, currentDepth);
        return 0.0; // Fully lit (no shadow)
    }

    // Calculate variance with improved stability
    float variance = moments.y - (moments.x * moments.x);
    variance = max(variance, 0.0001); // Increased minimum variance for smoother shadows

    // Calculate probability using Chebyshev's inequality
    float d = normalizedDepth - moments.x;
    float p_max = variance / (variance + d * d);

    // Improved light bleeding reduction with smoother transition
    float amount = 0.2; // Reduced for smoother shadows
    p_max = clamp((p_max - amount) / (1.0 - amount), 0.0, 1.0);

    // Apply additional smoothing to the shadow factor
    p_max = smoothstep(0.0, 1.0, p_max);

    // Compute fade factor for blending to black outside the range
    float fade = smoothstep(lights[lightIndex].radius * 0.65, lights[lightIndex].radius, currentDepth);
    outFade = fade;

    // If outside the range, force shadow to 1.0 (fully shadowed)
    if(currentDepth >= lights[lightIndex].radius)
    {
        return 1.0; // Fully shadowed
    }

    return 1.0 - p_max; // Invert VSM result: 1.0 = shadowed, 0.0 = lit
}

vec3 normalFromGreen(vec2 texCoords) {
    float g = texture(albedo, texCoords).g;
    // Map green from [0,1] to [-1,1] for Y
    float y = g * 2.0 - 1.0;
    return normalize(vec3(0.0, y, 1.0));
}

// Generate a basic tangent space normal from the albedo texture using a Sobel filter for height and a depth scale
vec3 normalFromAlbedoSobel(vec2 texCoords, float depthScale)
{
    // Sobel kernels for X and Y
    float kernelX[9] = float[](
        -1, 0, 1,
        -2, 0, 2,
        -1, 0, 1
    );
    float kernelY[9] = float[](
        -1, -2, -1,
         0,  0,  0,
         1,  2,  1
    );

    // Sample offsets (assuming texture coordinates in [0,1])
    vec2 texelSize = 1.0 / vec2(textureSize(albedo, 0));
    float height[9];
    int idx = 0;
    for (int y = -1; y <= 1; ++y) {
        for (int x = -1; x <= 1; ++x) {
            vec2 offset = vec2(float(x), float(y)) * texelSize;
            // Use luminance or just the green channel for height
            float h = texture(albedo, texCoords + offset).g;
            height[idx++] = h;
        }
    }

    // Apply Sobel filter
    float dX = 0.0;
    float dY = 0.0;
    for (int i = 0; i < 9; ++i) {
        dX += height[i] * kernelX[i];
        dY += height[i] * kernelY[i];
    }

    // Construct normal in tangent space
    vec3 normal;
    normal.x = -dX * depthScale;
    normal.y = -dY * depthScale;
    normal.z = 1.0;
    return normalize(normal);
}

void main() {

    vec4 texColor = texture(albedo, fs_in.TexCoords);

    if (discardTransparent && texColor.a < 0.1)
    {
        discard;
    }

    vec3 normalMap = texture(normalMap, fs_in.TexCoords).rgb;
    normalMap = normalMap * 2.0 - 1.0;

    normalMap = normalFromAlbedoSobel(fs_in.TexCoords, 0.8); // Use Sobel filter for normal map
    // Ensure normalMap is in the range [-1, 1]
    normalMap = normalize(normalMap);

    vec3 T = normalize(fs_in.fragTangent);
    vec3 N = normalize(fs_in.fragNormal);
    vec3 B = cross(N, T);
    mat3 TBN = mat3(T, B, N);

    vec3 normal = normalize(TBN * normalMap);
    vec3 viewDir = normalize(viewPos - fs_in.FragPos);

    vec3 totalDiffuse = vec3(0.0);
    vec3 totalSpecular = vec3(0.0);

    for (int lightIndex = 0; lightIndex < lightCount; ++lightIndex)
    {
        vec3 lightDir = normalize(lights[lightIndex].position - fs_in.FragPos);
        float distance = length(lights[lightIndex].position - fs_in.FragPos);
        float attenuation = 1.0f;

        float diff = max(dot(normal, lightDir), 0.0);
        vec3 diffuse = diff * lights[lightIndex].color * attenuation;

        // Only compute specular if facing the light
        float specularStrength = 0.5;
        float shininess = 32.0;
        float spec = 0.0;
        if(diff > 0.0)
        {
            // Use Blinn-Phong for more accurate specular
            vec3 halfwayDir = normalize(lightDir + viewDir);
            spec = pow(max(dot(normal, halfwayDir), 0.0), shininess);
        }
        vec3 specular = specularStrength * spec * vec3(1.0, 1.0, 1.0) * attenuation;

        // Calculate shadow for this light using VSM
        float fade;
        float pointShadow = VSMPointShadowCalculation(fs_in.FragPos, lightIndex, fade);

        if(!lights[lightIndex].shadowEnabled)
        {
            pointShadow = 0.0; // No shadow if shadow is disabled
        }

        // Smoothly blend shadow using smoothstep
        float shadowFactor = smoothstep(0.4, 0.6, pointShadow);

        // Fade out the light smoothly as we approach far_plane
        float lightVisibility = 1.0 - max(shadowFactor, fade);

        totalDiffuse += diffuse * lightVisibility;
        totalSpecular += specular * lightVisibility;
    }

    // --- Directional light contribution with shadow ---
    vec3 dirLightDir = -normalize(mainDirectionalLightDirection); // Negate because we want light-to-surface direction
    float dirDiff = max(dot(normal, dirLightDir), 0.0);
    vec3 dirDiffuse = dirDiff * mainDirectionalLightColor * mainDirectionalLightIntensity;
    

    float specularStrength = 0.5;
    float shininess = 32.0;

    float dirSpec = 0.0;
    
    if(dirDiff > 0.0)
    {
        // Use Blinn-Phong for more accurate specular
        vec3 dirHalfwayDir = normalize(dirLightDir + viewDir);
        dirSpec = pow(max(dot(normal, dirHalfwayDir), 0.0), shininess);
    }

    vec3 dirSpecular = specularStrength * dirSpec * vec3(1.0, 1.0, 1.0); // Diffuse color

    // Calculate shadow for directional light using VSM
    float dirShadow = VSMDirectionalShadow(fs_in.FragPosLightSpace);

    // Smoothly blend shadow using smoothstep
    float dirShadowFactor = smoothstep(0.4, 0.6, dirShadow);
    // Fade out the directional light smoothly as we approach far_plane
    float dirLightVisibility = 1.0 - dirShadowFactor;
    totalDiffuse += dirDiffuse * dirLightVisibility;
    totalSpecular += dirSpecular * dirLightVisibility;
    // --- End of directional light contribution ---

    // Final color
    vec3 tintedColor = texColor.rgb * mainColor.rgb;
    vec3 finalColor = tintedColor * fs_in.fragColor.rgb * (totalDiffuse + totalSpecular + ambientLight);

    outColor = vec4(finalColor, texColor.a * fs_in.fragColor.a);
}