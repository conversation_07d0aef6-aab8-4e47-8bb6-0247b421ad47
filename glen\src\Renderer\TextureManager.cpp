#include "TextureManager.h"

#include <GLFW/glfw3.h>

#include <filesystem>
#include <Renderer/TextureFormats/Dtx.h>

TextureManager::TextureManager()
{
    m_pTextures = std::unordered_map<std::string, std::shared_ptr<Texture>>();
}

TextureManager::~TextureManager()
{
    for (auto &pair : m_pTextures)
    {
        pair.second.reset();
    }
}


void TextureManager::LoadTexture(const std::string &textureName)
{
    std::shared_ptr<Texture> texture = std::make_shared<Texture>();

    //strip the file name from the path
    std::string fileName = textureName.substr(textureName.find_last_of("\\") + 1);
    std::string filePath = textureName.substr(0, textureName.find_last_of("\\"));
    std::string fileNameWithoutExtension = fileName.substr(0, fileName.find_last_of("."));
    std::string fileExtension = fileName.substr(fileName.find_last_of(".") + 1);

    bool isEditorIcon = false;
    //if textureName contains "editor"
    if (textureName.find("editor") != std::string::npos)
    {
        isEditorIcon = true;
    }

    if(!isEditorIcon)
    {
    //strip the resources\textures\ from the file path
    filePath = filePath.substr(filePath.find("resources\\textures\\") + 19);
    filePath.append("\\");
    filePath.append(fileNameWithoutExtension);
    }
    else
    {
        filePath = fileNameWithoutExtension;
    }

    
    
    // Get the file type from the file name
    Texture::TextureFileType fileType = texture->GetFileTypeFromFile(textureName);

    switch (fileType)
    {
    case Texture::TextureFileType::TGA:
        texture->LoadTGA(textureName);
        break;
    case Texture::TextureFileType::PNG:
        texture->LoadPNG(textureName);
        break;
    case Texture::TextureFileType::JPG:
        texture->LoadJPG(textureName);
        break;
    case Texture::TextureFileType::BMP:
        texture->LoadBMP(textureName);
        break;
    case Texture::TextureFileType::DDS:
        texture->LoadDDS(textureName);
        break;
    case Texture::TextureFileType::DTX: 
        filePath.append(".DTX");
        texture = DTX::LoadDTX(textureName);
        break;
    default:
        break;
    }
    m_pTextures[filePath] = texture;
}

void TextureManager::DeleteTexture(const std::string &textureName)
{
    m_pTextures.erase(textureName);
}

std::shared_ptr<Texture> TextureManager::GetTexture(const std::string &textureName)
{
    return m_pTextures[textureName];
}


void TextureManager::CopyAllTexturesToProjectFolder()
{
    // Base texture path and project texture path
    std::string baseTexturePath = "C:\\Program Files (x86)\\Aliens vs. Predator 2\\AVP2\\WorldTextures";
    std::string projectTexturePath = "resources\\textures";

    // Create the project texture directory if it doesn't exist
    try
    {
        std::filesystem::create_directories(projectTexturePath);
    }
    catch (const std::filesystem::filesystem_error &e)
    {
        std::cerr << "Error creating directory: " << e.what() << std::endl;
        return;
    }

    // Loop through our texture manager and copy all textures to the project folder that are in the list
    for (auto &texture : m_pTextures)
    {
        std::string textureName = texture.first;
        std::string pathToTexture = baseTexturePath + "\\" + textureName;
        std::string destinationPath = projectTexturePath + "\\" + textureName;

        // Check if the source file exists
        if (!std::filesystem::exists(pathToTexture))
        {
            std::cerr << "Source file does not exist: " << pathToTexture << std::endl;
            continue;
        }

        // Create the destination directory if it doesn't exist
        try
        {
            std::filesystem::create_directories(std::filesystem::path(destinationPath).parent_path());
        }
        catch (const std::filesystem::filesystem_error &e)
        {
            std::cerr << "Error creating directory: " << e.what() << std::endl;
            continue;
        }

        // Copy the file
        try
        {
            std::filesystem::copy_file(pathToTexture, destinationPath, std::filesystem::copy_options::overwrite_existing);
            std::cout << "Copied " << pathToTexture << " to " << destinationPath << std::endl;
        }
        catch (const std::filesystem::filesystem_error &e)
        {
            std::cerr << "Error copying file: " << e.what() << std::endl;
        }
    }
}
