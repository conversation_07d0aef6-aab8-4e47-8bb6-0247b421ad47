#pragma once
#include <string>
#include <vector>

class Scene;
class GameObject;
class InputManager;
class Time;

struct FieldInfo {
    std::string name;
    std::string type;
    void* ptr; // Pointer to the field in the component
};

class IComponent {
public:
    virtual ~IComponent() {}
    virtual void Start() = 0;
    virtual void Update() = 0;
    virtual const char* GetTypeName() const = 0;
    virtual void SetGameObject(GameObject* obj) = 0;
    virtual void SetInput(InputManager* input)
    {
        this->input = input;
    }

    virtual void SetTime(Time* time)
    {
        this->time = time;
    }

    virtual void SetScene(Scene* scene)
    {
        this->scene = scene;
    }

    virtual void PrintMembers() = 0; // Added to print members
    virtual bool SetField(const std::string& name, const std::string& value) = 0;
    virtual std::string GetField(const std::string& name) const = 0;
    //virtual std::vector<BuiltInFieldInfo> GetBuiltInFields() { return std::vector<BuiltInFieldInfo>(); };

    InputManager *input = nullptr; // Pointer to Input class for handling input events
    Time *time = nullptr; // Pointer to Time class for handling time events
    Scene *scene = nullptr; 
};
