#pragma once

#include <AL/al.h>
#include <AL/alc.h>
#include <unordered_map>
#include <string>
#include <vector>
#include <memory>
#include <iostream>
#include <glm/glm.hpp>

class SoundManager
{
public:
    typedef struct SoundProperties
    {
        std::string name;
        bool Loop = false;
        bool Is3D = false;
        float Volume = 100.0f;
        float Pitch = 1.0f;
        float MinDistance = 1.0f;
        float MaxDistance = 100.0f;
        float RolloffFactor = 1.0f;
    } SoundProperties;

    static SoundManager &GetInstance()
    {
        static SoundManager instance;
        return instance;
    }

    SoundManager();
    ~SoundManager();

    bool LoadAudioFile(const std::string &name, const std::string &filename, bool forceMono = false);
    void Play(const SoundProperties &soundProperties);
    void PlayAt(const SoundProperties &soundProperties, const glm::vec3 &position);
    void Stop(const SoundProperties &soundProperties);
    void SetListenerPosition(const glm::vec3 &position, const glm::vec3 &forward, const glm::vec3 &up);
    void UpdateSoundPosition(const SoundProperties &soundProperties, const glm::vec3 &pos);
    void UpdateSoundData(const SoundProperties &soundData);
    void Cleanup();

private:
    typedef struct SoundData
    {
        ALuint buffer = 0;
        ALuint source = 0;
    } SoundData;

    enum class AudioFormat
    {
        UNKNOWN = 0,
        PCM = 1,
        IEEE_FLOAT = 3,
        MP3 = 85,
    };

    ALCdevice *m_pDevice = nullptr;
    ALCcontext *m_pContext = nullptr;
    std::unordered_map<std::string, SoundData> m_vSounds;

    bool LoadWAVFile(const std::string &filename, ALenum &format, std::vector<char> &data, ALsizei &freq, int &numChannels, int &bitsPerSample);
    bool LoadMP3File(const std::string &filename, ALenum &format, std::vector<char> &data, ALsizei &freq, int &numChannels, int &bitsPerSample, bool forceMono);
};