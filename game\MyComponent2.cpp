// MyComponent.cpp

#include "MyComponent2.h"

#include <glen.h>
#include <iostream>

void MyComponent2::Start()
{

}

void MyComponent2::Update()
{
    if(!test)
    {
        printf("MyComponent2::Update() - test is false, skipping update\n");
    }

    if (input->IsKeyPressed(GLFW_KEY_O))
    {
        std::cout << "MyComponent2::Update() - O key pressed\n";
    }
   
}

const char *MyComponent2::GetTypeName() const
{
    return "MyComponent2";
}

void MyComponent2::SetGameObject(GameObject *obj)
{
    std::cout << "GameObject name: " << obj->GetName() << "\n";
    go = obj;
}


std::vector<BuiltInFieldInfo> MyComponent2::GetBuiltInFields()
{
    std::vector<BuiltInFieldInfo> fields;
    fields.reserve(1);
    fields.push_back(BuiltInFieldInfo{"bool", "test", &test});
    return fields;
}