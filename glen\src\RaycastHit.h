#pragma once

#include <glad/gl.h>
#include <glm/glm.hpp>
#include "Components/Transform.h"

struct RaycastHit
{
    glm::vec3 barycentric; // Barycentric coordinates of the intersection point
    glm::vec3 uv;         // UV coordinates of the intersection point
    glm::vec3 point;   // The point of intersection
    glm::vec3 normal;  // The normal at the point of intersection
    float distance;    // Distance from the ray origin to the intersection point
    void* collider;    // Pointer to the collider that was hit (if any)
    Transform* transform; // Pointer to the transform of the object that was hit
    uint32_t triangleIndex; // Index of the triangle that was hit
};