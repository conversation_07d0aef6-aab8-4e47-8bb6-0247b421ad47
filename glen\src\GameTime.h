#pragma once

#include <GLFW/glfw3.h>

//double lastTime = glfwGetTime();


//static Time class
class Time
{
public:
    static double deltaTime; // Time between frames
    static double time; // Total time since the start of the program
    static double lastTime; // Time of the last frame
    static float timeScale; // Time scale for the game (1.0 = normal speed, 0.5 = half speed, 2.0 = double speed)
    static bool isPaused; // Is the game paused?
    static bool isRunning; // Is the game running?

    static void Update();

    static float GetDeltaTime();
    
    static double GetTime();
    static double GetLastTime();
    static void Reset();
    static void SetTime(double t);
    static void SetDeltaTime(double dt);
    static void SetLastTime(double lt);
    static void SetTimeScale(float ts);
    static float GetTimeScale();
    static void SetPaused(bool paused);
    static bool IsPaused();
    static void SetRunning(bool running);
    static bool IsRunning();

    double* m_pDeltaTime;
    double GetDelta() const { return *m_pDeltaTime; }

    Time()
    {
        m_pDeltaTime = &deltaTime;
    }
    
    ~Time() {}
};

extern Time* g_Time;
