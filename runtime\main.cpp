#include <glen.h>
#include <filesystem>
#include <windows.h>

int main(int argc, char *argv[])
{

    Renderer::Init();

    auto scene = std::make_shared<Scene>();
    scene->SetName("MainScene");
    Scene::SetActiveScene(scene);

    auto gameObject = std::make_shared<GameObject>("MainObject");

    auto mesh = std::make_shared<Mesh>("resources/models/meshes.bin");

    // loop through the submeshes and set the material
    for (auto &subMesh : mesh->subMeshes)
    {
        // std::string pathToTexture = "C:\\Program Files (x86)\\Aliens vs. Predator 2\\AVP2\\WorldTextures\\" + subMesh.texture;
        std::string pathToTexture = "resources\\textures\\" + subMesh.texture;
        auto texture = DTX::LoadDTX(pathToTexture);
        //Renderer::GetTextureManager()->AddTexture(subMesh.texture, texture);
        Renderer::GetTextureManager()->LoadTexture(pathToTexture);
    }

    Renderer::GetTextureManager()->LoadTexture("resources\\textures\\normal.dtx");


    auto meshRenderer = std::make_shared<MeshRenderer>(mesh);
    gameObject->AddComponent(meshRenderer);
    gameObject->GetComponent<Transform>()->SetPosition(glm::vec3(0.0f, 0.0f, 0.0f));
    gameObject->GetComponent<Transform>()->Rotate(glm::vec3(90.0f, 180.0f, 180.0f));
    gameObject->GetComponent<Transform>()->SetScale(glm::vec3(0.5f));
    AddGameObject(gameObject);

    auto test = std::make_shared<GameObject>("TestObject");
    std::shared_ptr<Camera> camera = std::make_shared<Camera>();
    test->AddComponent(camera);

    AddGameObject(test);

    // make 3 new game objects with lights
    auto light1 = std::make_shared<GameObject>("Light1");
    auto light2 = std::make_shared<GameObject>("Light2");
    auto light3 = std::make_shared<GameObject>("Light3");

    light1->AddComponent(std::make_shared<Light>());
    light2->AddComponent(std::make_shared<Light>());
    light3->AddComponent(std::make_shared<Light>());

    AddGameObject(light1);
    AddGameObject(light2);
    AddGameObject(light3);

    // Create a directional light for better scene lighting
    auto directionalLight = std::make_shared<GameObject>("DirectionalLight");
    directionalLight->AddComponent(std::make_shared<Light>());
    AddGameObject(directionalLight);

    // Configure the directional light
    directionalLight->GetComponent<Light>()->SetColor(glm::vec3(1.0f, 1.0f, 0.9f));
    directionalLight->GetComponent<Light>()->m_Type = Light::LightType::DIRECTIONAL;
    directionalLight->GetComponent<Light>()->m_fIntensity = 5.0f; // Higher intensity for directional light (sun-like)
    directionalLight->GetComponent<Light>()->m_bShadowEnabled = true;
    directionalLight->GetComponent<Light>()->m_bSoftShadows = true; // Enable soft shadows
    directionalLight->GetComponent<Light>()->m_iShadowBlurIterations = 8; // Good quality blur

    // Position and orient the directional light (simulating sun from above and slightly angled)
    directionalLight->GetComponent<Transform>()->SetPosition(glm::vec3(8.0f, 30.0f, 12.0f));
    directionalLight->GetComponent<Transform>()->SetRotation(glm::vec3(glm::radians(-60.0f), glm::radians(30.0f), 0.0f));

    light1->GetComponent<Light>()->SetColor(glm::vec3(1.0f, 0.8f, 0.9f));
    light1->GetComponent<Light>()->m_Type = Light::LightType::POINT;
    light1->GetComponent<Light>()->m_fRange = 10.0f;
    light1->GetComponent<Light>()->m_bShadowEnabled = false;

    light2->GetComponent<Light>()->SetColor(glm::vec3(0.8f, 0.6f, 0.9f));
    light2->GetComponent<Light>()->m_Type = Light::LightType::POINT;
    light2->GetComponent<Light>()->m_fRange = 10.0f;
    light2->GetComponent<Light>()->m_bShadowEnabled = false;

    light3->GetComponent<Light>()->SetColor(glm::vec3(0.6f, 0.8f, 0.9f));
    light3->GetComponent<Light>()->m_Type = Light::LightType::POINT;
    light3->GetComponent<Light>()->m_fRange = 10.0f;
    light3->GetComponent<Light>()->m_bShadowEnabled = false;

    Renderer::SetAmbientLight(glm::vec3(0.1f, 0.1f, 0.1f));



    SoundManager::GetInstance().LoadAudioFile("test", "resources/sounds/FIRE_SONG.WAV");
    SoundManager::GetInstance().LoadAudioFile("chainlift", "resources/sounds/chainlift.wav");


    auto soundObject = std::make_shared<Sound>("chainlift");
    soundObject->Set3D(true);
    soundObject->SetLoop(true);
    soundObject->SetVolume(25.0f);
    light3->AddComponent(soundObject);

    auto soundObject2 = std::make_shared<Sound>("test");
    soundObject2->Set3D(false);
    soundObject2->SetLoop(true);
    soundObject2->SetVolume(100.0f);
    light2->AddComponent(soundObject2);

    HMODULE lib = LoadLibraryA("glen-game.dll");
    if (!lib)
    {
        std::cerr << "Failed to load DLL\n";
        return 1;
    }

    using CreateFunc = IComponent *(*)(const char *);
    CreateFunc CreateComponent = (CreateFunc)GetProcAddress(lib, "CreateComponent");

    using SetOpenGLContextFunc = void (*)(HGLRC, HDC);
    SetOpenGLContextFunc SetOpenGLContext = (SetOpenGLContextFunc)GetProcAddress(lib, "SetOpenGLContext");

    HGLRC hglrc = wglGetCurrentContext();
    if (!hglrc)
    {
        std::cerr << "Failed to get current OpenGL context\n";
        return 1;
    }
    //hdc
    HDC hdc = wglGetCurrentDC();
    if (!hdc)
    {
        std::cerr << "Failed to get current OpenGL device context\n";
        return 1;
    }

    SetOpenGLContext(hglrc, hdc);

    IComponent *comp = CreateComponent("MyComponent");
    comp->SetInput(&InputManager::GetInstance());
    comp->SetTime(g_Time);
    comp->SetScene(scene.get());
    auto compShared = std::shared_ptr<IComponent>(comp);
    test->AddComponent(std::dynamic_pointer_cast<Component>(compShared));

    IComponent *comp2 = CreateComponent("MyComponent2");
    comp2->SetInput(&InputManager::GetInstance());
    comp2->SetTime(g_Time);
    comp2->SetScene(scene.get());
    auto comp2Shared = std::shared_ptr<IComponent>(comp2);
    test->AddComponent(std::dynamic_pointer_cast<Component>(comp2Shared));

    Renderer::SetActiveScene(scene.get());

    meshRenderer->BuildMaterialList();

    scene->Start();

    

    while (!Renderer::ShouldClose())
    {

        Time::Update();

        InputManager::Update();
        glfwPollEvents();

        std::vector<Light *> lights;

        auto gameObjects = scene->GetGameObjects();
        for (auto &gameObject : gameObjects)
        {
            auto lightComponent = gameObject->GetComponent<Light>();
            if (lightComponent)
            {
                lights.push_back(lightComponent.get());
            }
        }

        if (InputManager::GetInstance().IsKeyPressed(GLFW_KEY_1))
        {
            lights[0]->GetGameObject()->GetTransform()->SetPosition(camera->GetGameObject()->GetTransform()->GetPosition());
        }
        if (InputManager::GetInstance().IsKeyPressed(GLFW_KEY_2))
        {
            lights[1]->GetGameObject()->GetTransform()->SetPosition(camera->GetGameObject()->GetTransform()->GetPosition());
        }
        if (InputManager::GetInstance().IsKeyPressed(GLFW_KEY_3))
        {
            lights[2]->GetGameObject()->GetTransform()->SetPosition(camera->GetGameObject()->GetTransform()->GetPosition());
        }

        if (InputManager::GetInstance().IsKeyDown(GLFW_KEY_UP))
        {
            lights[0]->m_fRange += 5.0f * Time::GetDeltaTime();
            lights[1]->m_fRange += 5.0f * Time::GetDeltaTime();
            lights[2]->m_fRange += 5.0f * Time::GetDeltaTime();
        }
        if (InputManager::GetInstance().IsKeyDown(GLFW_KEY_DOWN))
        {
            lights[0]->m_fRange -= 5.0f * Time::GetDeltaTime();
            lights[1]->m_fRange -= 5.0f * Time::GetDeltaTime();
            lights[2]->m_fRange -= 5.0f * Time::GetDeltaTime();
        }

        if (InputManager::GetInstance().IsKeyPressed(GLFW_KEY_F))
        {
            meshRenderer->BuildMaterialList();
            //Renderer::lightPos = camera->GetGameObject()->GetTransform()->GetPosition();
        }

        // Light controls removed - now handled by directional light components
        // if (InputManager::GetInstance().IsKeyPressed(GLFW_KEY_G))
        // {
        //     Renderer::lightLookAtPos = camera->GetGameObject()->GetTransform()->GetPosition();
        // }

        // if (InputManager::GetInstance().IsKeyDown(GLFW_KEY_LEFT))
        // {
        //     Renderer::shadowBias += 0.0001f;
        // }
        // if (InputManager::GetInstance().IsKeyDown(GLFW_KEY_RIGHT))
        // {
        //     Renderer::shadowBias -= 0.0001f;
        // }

        // ijkl to move mesh
        if (InputManager::GetInstance().IsKeyDown(GLFW_KEY_K))
        {
            gameObject->GetComponent<Transform>()->Translate(glm::vec3(0.0f, 0.0f, -1.0f) * Time::GetDeltaTime());
        }
        if (InputManager::GetInstance().IsKeyDown(GLFW_KEY_I))
        {
            gameObject->GetComponent<Transform>()->Translate(glm::vec3(0.0f, 0.0f, 1.0f) * Time::GetDeltaTime());
        }
        if (InputManager::GetInstance().IsKeyDown(GLFW_KEY_J))
        {
            gameObject->GetComponent<Transform>()->Translate(glm::vec3(-1.0f, 0.0f, 0.0f) * Time::GetDeltaTime());
        }
        if (InputManager::GetInstance().IsKeyDown(GLFW_KEY_L))
        {
            gameObject->GetComponent<Transform>()->Translate(glm::vec3(1.0f, 0.0f, 0.0f) * Time::GetDeltaTime());
        }
        if (InputManager::GetInstance().IsKeyDown(GLFW_KEY_O))
        {
            gameObject->GetComponent<Transform>()->Translate(glm::vec3(0.0f, -1.0f, 0.0f) * Time::GetDeltaTime());
        }
        if (InputManager::GetInstance().IsKeyDown(GLFW_KEY_U))
        {
            gameObject->GetComponent<Transform>()->Translate(glm::vec3(0.0f, 1.0f, 0.0f) * Time::GetDeltaTime());
        }

        scene->Update();

        Renderer::Render();
    }

    Renderer::Shutdown();

    scene->RemoveGameObject(gameObject);
    scene->RemoveGameObject(test);

    glfwDestroyWindow(Renderer::GetWindowPtr());
    glfwTerminate();
    return 0;
}