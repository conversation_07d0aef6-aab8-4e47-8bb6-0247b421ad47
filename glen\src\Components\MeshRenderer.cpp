#include "MeshRenderer.h"
#include <Renderer/Renderer.h>
#include "Components/Transform.h"
#include "debug.h"

static std::string GenerateUUID();

MeshRenderer::MeshRenderer()
{
    m_pMesh = nullptr;

    m_UUID = GenerateUUID();
    name = "Mesh<PERSON><PERSON><PERSON>";
}

MeshRenderer::MeshRenderer(std::shared_ptr<Mesh> mesh)
{
    m_pMesh = mesh;
    m_UUID = GenerateUUID();
    name = "MeshRenderer";
}

MeshRenderer::~MeshRenderer()
{
    Debug::Log(ReturnTypes::SUCCESS, "MeshRenderer::~MeshRenderer() - MeshRenderer component destroyed");
    Renderer::RemoveMeshRenderer(m_UUID);
}

void MeshRenderer::BlurShadowMap(GLuint inputTex, GLuint outputTex, GLuint blurShader, bool horizontal)
{

    GLuint quadVAO, quadVBO;

    //setup quad for rendering frame buffer
    float quadVertices[] = {
        // positions        // texture Coords
        -1.0f, 1.0f, 0.0f, 0.0f, 1.0f,
        -1.0f, -1.0f, 0.0f, 0.0f, 0.0f,
        1.0f, -1.0f, 0.0f, 1.0f, 0.0f,

        -1.0f, 1.0f, 0.0f, 0.0f, 1.0f,
        1.0f, -1.0f, 0.0f, 1.0f, 0.0f,
        1.0f, 1.0f, 0.0f, 1.0f, 1.0f
    };

    glGenVertexArrays(1, &quadVAO);
    glGenBuffers(1, &quadVBO);
    glBindVertexArray(quadVAO);
    glBindBuffer(GL_ARRAY_BUFFER, quadVBO);
    glBufferData(GL_ARRAY_BUFFER, sizeof(quadVertices), &quadVertices, GL_STATIC_DRAW);
    glEnableVertexAttribArray(0);
    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, 5 * sizeof(float), (void*)0);
    glEnableVertexAttribArray(1);
    glVertexAttribPointer(1, 2, GL_FLOAT, GL_FALSE, 5 * sizeof(float), (void*)(3 * sizeof(float)));
    glBindVertexArray(0);

    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
    //bind the blur shader
    glUseProgram(blurShader);
    glUniform1i(glGetUniformLocation(blurShader, "image"), 0);
    glUniform1i(glGetUniformLocation(blurShader, "horizontal"), horizontal ? 1 : 0);

    glActiveTexture(GL_TEXTURE0);
    glBindTexture(GL_TEXTURE_2D, inputTex);

    glBindFramebuffer(GL_FRAMEBUFFER, Renderer::m_pBlurredShadowBuffer->GetFBO());
    glFramebufferTexture2D(GL_FRAMEBUFFER, GL_COLOR_ATTACHMENT0, GL_TEXTURE_2D, outputTex, 0);

    //bind quad VAO
    glBindVertexArray(quadVAO);

    glDrawArrays(GL_TRIANGLES, 0, 6);
    glBindTexture(GL_TEXTURE_2D, 0);

    glBindFramebuffer(GL_FRAMEBUFFER, 0);
}

void MeshRenderer::Render(Camera* camera)
{
    auto material = m_pMaterials[0];
    material->UseMaterial();

    GLuint modelLoc = glGetUniformLocation(material->GetShader()->GetShaderID(), "model");
    GLuint viewLoc = glGetUniformLocation(material->GetShader()->GetShaderID(), "view");
    GLuint projectionLoc = glGetUniformLocation(material->GetShader()->GetShaderID(), "projection");


    auto go = camera->GetGameObject();
    auto transform = go->GetTransform();

    auto cameraPos = transform->GetPosition();
    auto cameraFront = transform->GetForwardVector();
    auto cameraUp = transform->GetUpVector();

    //Renderer::viewMatrix = glm::lookAt(camera->GetTransform()->?
    Renderer::viewMatrix = glm::lookAt(cameraPos, cameraPos + cameraFront, cameraUp);
    
    // Set up the model matrix
    glm::mat4 model = glm::mat4(1.0f); // Identity matrix
    glm::vec3 position;
    glm::vec3 rotation;
    glm::vec3 scale;

    position = this->GetGameObject()->GetTransform()->GetPosition();
    rotation = this->GetGameObject()->GetTransform()->GetRotation();
    scale = this->GetGameObject()->GetTransform()->GetScale();

    model = glm::translate(model, position);
    model = glm::rotate(model, rotation.x, glm::vec3(1.0f, 0.0f, 0.0f));
    model = glm::rotate(model, rotation.y, glm::vec3(0.0f, 1.0f, 0.0f));
    model = glm::rotate(model, rotation.z, glm::vec3(0.0f, 0.0f, 1.0f));
    model = glm::scale(model, scale);

    //camera->CalculateAspectRatio(Renderer::GetWindowWidth(), Renderer::GetWindowHeight());
    camera->aspectRatio;

    Renderer::projectionMatrix = glm::perspective(glm::radians(camera->fov), //FOV
    camera->aspectRatio, //SIZE
    camera->nearClip, //NEAR CLIP PLANE
    camera->farClip); //FAR CLIP PLANE

    glUniformMatrix4fv(modelLoc, 1, GL_FALSE, glm::value_ptr(model));
    glUniformMatrix4fv(viewLoc, 1, GL_FALSE, glm::value_ptr(Renderer::viewMatrix));
    glUniformMatrix4fv(projectionLoc, 1, GL_FALSE, glm::value_ptr(Renderer::projectionMatrix));


    material->GetShader()->SetVec3(Renderer::GetAmbientLight(), "ambientLight");

    //viewPos
    material->GetShader()->SetVec3(cameraPos, "viewPos");

    // Find directional light for main lighting and shadows
    Light* directionalLight = nullptr;
    auto lights = Renderer::GetLights(); // Use the recursive GetLights function
    for (auto& light : lights)
    {
        if (light && light->m_Type == Light::LightType::DIRECTIONAL)
        {
            directionalLight = light;
            break; // Use the first directional light found
        }
    }

    // Set directional light uniforms
    if (directionalLight)
    {
        // Use the light's direction for lighting calculations
        glm::vec3 lightDirection = directionalLight->GetDirectionalLightDirection();
        material->GetShader()->SetVec3(lightDirection, "mainDirectionalLightDirection");

        // Set directional light color and intensity
        material->GetShader()->SetVec3(directionalLight->m_vColor, "mainDirectionalLightColor");
        material->GetShader()->SetFloat(directionalLight->m_fIntensity, "mainDirectionalLightIntensity");

        // Set light space matrix for shadows
        material->GetShader()->SetMat4(directionalLight->GetLightSpaceMatrix(), "lightSpaceMatrix");

        // Use a default shadow bias (could be made configurable per light)
        material->GetShader()->SetFloat(0.001f, "shadowBias");
    }
    else
    {
        // Fallback to default values if no directional light is found
        glm::vec3 defaultLightDirection(0.0f, -1.0f, 0.0f); // Downward direction
        material->GetShader()->SetVec3(defaultLightDirection, "mainDirectionalLightDirection");
        material->GetShader()->SetVec3(glm::vec3(1.0f, 1.0f, 1.0f), "mainDirectionalLightColor");
        material->GetShader()->SetFloat(1.0f, "mainDirectionalLightIntensity");
        material->GetShader()->SetMat4(Renderer::lightSpaceMatrix, "lightSpaceMatrix");
        material->GetShader()->SetFloat(0.001f, "shadowBias");
    }

    

    float minBias = 0.00005f; // Reduced minimum bias for tighter shadows
    float maxBias = 0.0005f; // Reduced maximum bias for tighter shadows

    material->GetShader()->SetFloat(minBias, "minBias");
    material->GetShader()->SetFloat(maxBias, "maxBias");

    std::vector<Light*> lights;
    for (auto& gameObject : gameObjects)
    {
        auto lightComponent = gameObject->GetComponent<Light>();
        if (lightComponent)
        {
            lights.push_back(lightComponent.get());
        }
    }

    //bind uniform Light lights[MAX_LIGHTS];
    for (int i = 0; i < lights.size(); i++)
    {
        m_pMaterials[0]->GetShader()->SetVec3(lights[i]->GetGameObject()->GetTransform()->GetWorldPosition(), "lights[" + std::to_string(i) + "].position");
        m_pMaterials[0]->GetShader()->SetVec3(lights[i]->m_vColor, "lights[" + std::to_string(i) + "].color");
        m_pMaterials[0]->GetShader()->SetFloat(lights[i]->m_fRange, "lights[" + std::to_string(i) + "].radius");
        m_pMaterials[0]->GetShader()->SetFloat(lights[i]->m_fIntensity, "lights[" + std::to_string(i) + "].intensity");
        m_pMaterials[0]->GetShader()->SetFloat(0.04, "lights[" + std::to_string(i) + "].shadowBias");
        m_pMaterials[0]->GetShader()->SetBool(lights[i]->m_bShadowEnabled, "lights[" + std::to_string(i) + "].shadowEnabled");
    }

    //set lightCount
    m_pMaterials[0]->GetShader()->SetInt(lights.size(), "lightCount");

    // //bind shadow map
    glActiveTexture(GL_TEXTURE1);
    glBindTexture(GL_TEXTURE_2D, Renderer::m_pBlurredShadowBuffer->GetTextureID());
    m_pMaterials[0]->GetShader()->SetInt(1, "shadowMap");

    //bind normal map
    glActiveTexture(GL_TEXTURE2);
    glBindTexture(GL_TEXTURE_2D, Renderer::GetTextureManager()->GetTexture("\\normal.DTX")->GetTextureID());
    m_pMaterials[0]->GetShader()->SetInt(2, "normalMap");

    // Bind point light shadow cubemap (for point lights)
    for (int i = 0; i < 10; i++)
    {
        glActiveTexture(GL_TEXTURE0 + 3 + i);
        GLuint texID = (i < lights.size()) ? lights[i]->GetShadowMap()->getShadowMapTexture() : 0;
        glBindTexture(GL_TEXTURE_CUBE_MAP, texID);

        // Get the uniform location by name
        std::string uniformName = "pointShadowMap[" + std::to_string(i) + "]";
        GLint location = glGetUniformLocation(m_pMaterials[0]->GetShader()->GetShaderID(), uniformName.c_str());
        glUniform1i(location, 3 + i); // Set the sampler to use texture unit 3 + i
    }

    //send far_plane to shader
    //calculate far_plane based on the light's range
    m_pMaterials[0]->GetShader()->SetFloat(100.00f, "far_plane");

    m_pMesh->DrawMesh(m_pMaterials[0]->GetShader(), *this, false);

    m_pMaterials[0]->GetShader()->Unbind();
}

void MeshRenderer::BlurShadowMapPingPong()
{
// Visualize the shadow map using a depth_to_color shader into m_pBlurredShadowBuffer
    Renderer::m_pBlurredShadowBuffer->Bind();
    glViewport(0, 0, Renderer::m_pBlurredShadowBuffer->GetWidth(), Renderer::m_pBlurredShadowBuffer->GetHeight());
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
    
    auto depthToColorShader = Renderer::GetShaderManager()->GetShader("depth_to_color");
    depthToColorShader->Bind();
    glActiveTexture(GL_TEXTURE0);
    glBindTexture(GL_TEXTURE_2D, Renderer::m_pShadowBuffer->GetTextureID());
    depthToColorShader->SetInt(0, "depthTex");
    glBindVertexArray(Renderer::quadVAO);
    glDrawArrays(GL_TRIANGLES, 0, 6);
    glBindVertexArray(0);
    glBindTexture(GL_TEXTURE_2D, 0);
    depthToColorShader->Unbind();
   Renderer::m_pBlurredShadowBuffer->Unbind();
    

        const int blurIterations = 8; // Use an even number (2, 4, 6, ...)
    bool horizontal = false;
    FrameBuffer* readBuffer = Renderer::m_pBlurredShadowBuffer.get();
    FrameBuffer* writeBuffer = Renderer::m_pPingPongShadowBuffer.get();
    
    for (int i = 0; i < blurIterations; ++i)
    {
        writeBuffer->Bind();
        glViewport(0, 0, writeBuffer->GetWidth(), writeBuffer->GetHeight());
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
    
        auto gaussianShader = Renderer::GetShaderManager()->GetShader("gaussian");
        gaussianShader->Bind();
        glActiveTexture(GL_TEXTURE0);
        glBindTexture(GL_TEXTURE_2D, readBuffer->GetTextureID());
        gaussianShader->SetInt(0, "image");
        gaussianShader->SetBool(horizontal, "horizontal");
    
        glBindVertexArray(Renderer::quadVAO);
        glDrawArrays(GL_TRIANGLES, 0, 6);
        glBindVertexArray(0);
        glBindTexture(GL_TEXTURE_2D, 0);
        gaussianShader->Unbind();
        writeBuffer->Unbind();
    
        // Swap buffers for next pass
        std::swap(readBuffer, writeBuffer);
        horizontal = !horizontal;
    }
    // After the loop, m_pBlurredShadowBuffer contains the result if blurIterations is even

}



void MeshRenderer::RenderDepth(glm::vec3 lightLookAtPos, glm::vec3 lightPos, Camera* camera)
{
    // DEBUG: Set this to true to enable camera-fitted shadows
    bool useCameraFittedShadows = true;

    if (useCameraFittedShadows && camera != nullptr)
    {
        // Calculate light direction from light position to look-at position
        glm::vec3 lightDirection = glm::normalize(lightLookAtPos - lightPos);

        // Use camera's near and far planes for shadow calculation
        float shadowNear = camera->nearClip;
        float shadowFar = camera->farClip;

        // DEBUG: Print camera position to see if it's changing
        static glm::vec3 lastCameraPos = glm::vec3(0.0f);
        glm::vec3 currentCameraPos = camera->GetGameObject()->GetTransform()->GetPosition();
        if (glm::distance(lastCameraPos, currentCameraPos) > 0.1f) {
            printf("Camera moved to: (%.2f, %.2f, %.2f)\n", currentCameraPos.x, currentCameraPos.y, currentCameraPos.z);
            lastCameraPos = currentCameraPos;
        }

        // Calculate fitted light space matrix based on camera frustum
        Renderer::lightSpaceMatrix = ShadowUtils::CalculateFittedLightSpaceMatrix(
            camera, lightDirection, shadowNear, shadowFar);
    }
    else
    {
        // Use old method (should work)
        float near_plane = 0.1f, far_plane = 1000.0f;
        float distance = 90.0f; // Default distance value
        glm::mat4 lightProjection = glm::ortho(-distance, distance, -distance, distance, near_plane, far_plane);
        glm::mat4 lightView = glm::lookAt(lightPos, lightLookAtPos, glm::vec3(0.0, 1.0, 0.0));
        Renderer::lightSpaceMatrix = lightProjection * lightView;
    }


    // Set up the model matrix
    glm::mat4 model = glm::mat4(1.0f); // Identity matrix
    // model = glm::rotate(model, glm::radians(-90.0f), glm::vec3(1.0f, 0.0f, 0.0f)); // Rotate 90 degrees on x-axis
    // model = glm::scale(model, glm::vec3(0.5f)); // Scale by 0.1

    glm::vec3 position;
    glm::vec3 rotation;
    glm::vec3 scale;

    position = this->GetGameObject()->GetTransform()->GetPosition();
    rotation = this->GetGameObject()->GetTransform()->GetRotation();
    scale = this->GetGameObject()->GetTransform()->GetScale();

    model = glm::translate(model, position);
    model = glm::rotate(model, rotation.x, glm::vec3(1.0f, 0.0f, 0.0f));
    model = glm::rotate(model, rotation.y, glm::vec3(0.0f, 1.0f, 0.0f));
    model = glm::rotate(model, rotation.z, glm::vec3(0.0f, 0.0f, 1.0f));
    model = glm::scale(model, scale);
    

    glViewport(0, 0, Renderer::m_pShadowBuffer->GetWidth(), Renderer::m_pShadowBuffer->GetHeight());
    glBindFramebuffer(GL_FRAMEBUFFER, Renderer::m_pShadowBuffer->GetFBO());
    glClear(GL_DEPTH_BUFFER_BIT);

    //disble backface culling
    glDisable(GL_CULL_FACE);


    auto shader = Renderer::GetShaderManager()->GetShader("vsm_depth");
    shader->Bind();

    shader->SetMat4(Renderer::lightSpaceMatrix, "lightSpaceMatrix");
    shader->SetMat4(model, "model");


    for (const auto &subMesh : m_pMesh->subMeshes)
    {
        glBindVertexArray(subMesh.VAO);
        glActiveTexture(GL_TEXTURE0);
        glBindTexture(GL_TEXTURE_2D, Renderer::GetTextureManager()->GetTexture(subMesh.texture)->GetTextureID());
        shader->SetInt(0, "albedo");
        shader->SetBool(subMesh.isAlphaTest, "discardTransparent");
        glDrawElements(GL_TRIANGLES, subMesh.indices->size(), GL_UNSIGNED_INT, 0);
        glBindTexture(GL_TEXTURE_2D, 0);
    }

    shader->Unbind();
    
    

    glEnable(GL_CULL_FACE);

    BlurShadowMapPingPong();

    // Reset viewport to the default window size
    glViewport(0, 0, Renderer::GetWindowWidth(), Renderer::GetWindowHeight());
}

void MeshRenderer::RenderDirectionalDepth(Light* directionalLight, Camera* camera)
{
    if (!directionalLight || directionalLight->m_Type != Light::LightType::DIRECTIONAL || !camera)
    {
        return;
    }

    // Get transform data
    auto transform = GetGameObject()->GetTransform();
    glm::vec3 position = transform->GetPosition();
    glm::vec3 rotation = transform->GetRotation();
    glm::vec3 scale = transform->GetScale();

    // Create model matrix
    glm::mat4 model = glm::mat4(1.0f);
    model = glm::translate(model, position);
    model = glm::rotate(model, rotation.x, glm::vec3(1.0f, 0.0f, 0.0f));
    model = glm::rotate(model, rotation.y, glm::vec3(0.0f, 1.0f, 0.0f));
    model = glm::rotate(model, rotation.z, glm::vec3(0.0f, 0.0f, 1.0f));
    model = glm::scale(model, scale);

    // Set up shadow buffer rendering
    glViewport(0, 0, Renderer::m_pShadowBuffer->GetWidth(), Renderer::m_pShadowBuffer->GetHeight());
    glBindFramebuffer(GL_FRAMEBUFFER, Renderer::m_pShadowBuffer->GetFBO());
    glClear(GL_DEPTH_BUFFER_BIT);

    // Disable backface culling
    //glDisable(GL_CULL_FACE);

    // Use VSM depth shader
    auto shader = Renderer::GetShaderManager()->GetShader("vsm_depth");
    shader->Bind();

    // Set shader uniforms using the directional light's light space matrix
    shader->SetMat4(directionalLight->GetLightSpaceMatrix(), "lightSpaceMatrix");
    shader->SetMat4(model, "model");

    // Render all submeshes
    for (const auto &subMesh : m_pMesh->subMeshes)
    {
        glBindVertexArray(subMesh.VAO);
        glActiveTexture(GL_TEXTURE0);
        glBindTexture(GL_TEXTURE_2D, Renderer::GetTextureManager()->GetTexture(subMesh.texture)->GetTextureID());
        shader->SetInt(0, "albedo");
        shader->SetBool(subMesh.isAlphaTest, "discardTransparent");
        glDrawElements(GL_TRIANGLES, subMesh.indices->size(), GL_UNSIGNED_INT, 0);
        glBindTexture(GL_TEXTURE_2D, 0);
    }

    shader->Unbind();

    // Re-enable backface culling
    glEnable(GL_CULL_FACE);

    // Apply blur to shadow map
    BlurShadowMapPingPong();

    // Reset viewport to the default window size
    glViewport(0, 0, Renderer::GetWindowWidth(), Renderer::GetWindowHeight());
}

void MeshRenderer::RenderPointDepth(Light* light)
{
    static const glm::vec3 directions[6] = {
        { 1,  0,  0}, // +X
        {-1,  0,  0}, // -X
        { 0,  1,  0}, // +Y
        { 0, -1,  0}, // -Y
        { 0,  0,  1}, // +Z
        { 0,  0, -1}  // -Z
    };
    static const glm::vec3 upVectors[6] = {
        {0, -1,  0}, // +X
        {0, -1,  0}, // -X
        {0,  0,  1}, // +Y
        {0,  0, -1}, // -Y
        {0, -1,  0}, // +Z
        {0, -1,  0}  // -Z
    };

    float aspect = 1.0f;
    float nearZ = 0.1f;
    float farZ = light->m_fRange;
    glm::mat4 shadowProj = glm::perspective(glm::radians(90.0f), aspect, nearZ, farZ);

    // Prepare the 6 shadow matrices for the geometry shader
    glm::vec3 lightWorldPos = light->GetGameObject()->GetTransform()->GetWorldPosition();
    glm::mat4 shadowMatrices[6];
    for (unsigned int i = 0; i < 6; ++i)
    {
        glm::mat4 view = glm::lookAt(lightWorldPos, lightWorldPos + directions[i], upVectors[i]);
        shadowMatrices[i] = shadowProj * view;
    }
    

    GLuint fbo = light->GetShadowMap()->getShadowMapFBO();
    GLuint cubemap = light->GetShadowMap()->getShadowMapTexture();
    glBindTexture(GL_TEXTURE_CUBE_MAP, cubemap);

    glViewport(0, 0, light->GetShadowMap()->getWidth(), light->GetShadowMap()->getHeight());
    glBindFramebuffer(GL_FRAMEBUFFER, fbo);

    auto shader = Renderer::GetShaderManager()->GetShader("vsm_point_depth"); // Use VSM point depth shader
    shader->Bind();
    shader->SetVec3(lightWorldPos, "lightPos");
    shader->SetFloat(farZ, "far_plane");

    // Upload all 6 shadow matrices
    for (unsigned int i = 0; i < 6; ++i)
    {
        shader->SetMat4(shadowMatrices[i], ("shadowMatrices[" + std::to_string(i) + "]").c_str());
    }

    // Set up the model matrix
    glm::mat4 model = glm::mat4(1.0f); // Identity matrix
    // model = glm::rotate(model, glm::radians(-90.0f), glm::vec3(1.0f, 0.0f, 0.0f)); // Rotate 90 degrees on x-axis
    // model = glm::scale(model, glm::vec3(0.5f)); // Scale by 0.1

    glm::vec3 position;
    glm::vec3 rotation;
    glm::vec3 scale;

    position = this->GetGameObject()->GetTransform()->GetPosition();
    rotation = this->GetGameObject()->GetTransform()->GetRotation();
    scale = this->GetGameObject()->GetTransform()->GetScale();

    model = glm::translate(model, position);
    model = glm::rotate(model, rotation.x, glm::vec3(1.0f, 0.0f, 0.0f));
    model = glm::rotate(model, rotation.y, glm::vec3(0.0f, 1.0f, 0.0f));
    model = glm::rotate(model, rotation.z, glm::vec3(0.0f, 0.0f, 1.0f));
    model = glm::scale(model, scale);

    shader->SetMat4(model, "model");

    glClear(GL_DEPTH_BUFFER_BIT);

    // Render all submeshes (no need to loop over faces)
    for (const auto& subMesh : m_pMesh->subMeshes)
    {
        glBindVertexArray(subMesh.VAO);
        glActiveTexture(GL_TEXTURE0);
        glBindTexture(GL_TEXTURE_2D, Renderer::GetTextureManager()->GetTexture(subMesh.texture)->GetTextureID());
        shader->SetInt(0, "albedo");
        shader->SetBool(subMesh.isAlphaTest, "discardTransparent");
        glDrawElements(GL_TRIANGLES, subMesh.indices->size(), GL_UNSIGNED_INT, 0);
        glBindTexture(GL_TEXTURE_2D, 0);
    }

    shader->Unbind();
    glBindFramebuffer(GL_FRAMEBUFFER, 0);
    glViewport(0, 0, Renderer::GetWindowWidth(), Renderer::GetWindowHeight());
}

void MeshRenderer::RenderPointLights(std::vector<Light*> lights)
{
    for (auto light : lights)
    {
        if( light->m_Type != Light::LightType::POINT)
            continue;
        if (!light->m_bShadowEnabled)
            continue;
        RenderPointDepth(light);
        // Apply blur to point light shadows for smoothness
        light->BlurShadowMap();
    }
}

void MeshRenderer::Update()
{

}

static std::string GenerateUUID()
{
    std::string uuid = "";
    for(int i = 0; i < 4; i++)
    {
        for(int j = 0; j < 4; j++)
        {
            uuid += std::to_string(rand() % 10);
        }
        if(i != 3)
        {
            uuid += "-";
        }
    }
    return uuid;
}

void MeshRenderer::Start()
{
    // Assuming 'this' is managed by shared_ptr elsewhere; otherwise, manage lifetime appropriately.
    Renderer::AddMeshRenderer(m_UUID, shared_from_this());

    Debug::Log(ReturnTypes::SUCCESS, "MeshRenderer::Start() - MeshRenderer added to queue for rendering");

    

}

void MeshRenderer::UpdateMesh()
{
    BuildMaterialList();
}

void MeshRenderer::BuildMaterialList()
{
    m_pMaterials.clear();

    int index = 0;
    //set the list to the size of the submeshes
    m_pMaterials.reserve(m_pMesh->subMeshes.size());
    printf("MeshRenderer::BuildMaterialList() - Building material list for mesh with %zu submeshes\n", m_pMesh->subMeshes.size());
    // loop through the submeshes and add the materials
    for (const auto &subMesh : m_pMesh->subMeshes)
    {
        //printf("MeshRenderer::BuildMaterialList() - Processing submesh %d with texture '%s'\n", index, subMesh.texture.c_str());
        if (!subMesh.texture.empty())
        {
            auto material = std::make_shared<Material>();
            material->SetShader(Renderer::GetShaderManager()->GetShader("default"));
            material->SetupMaterialUniforms();
            int textureID = Renderer::GetTextureManager()->GetTexture(subMesh.texture)->GetTextureID();
            material->SetUniformValue<int>("albedo", textureID);
            material->SetUniformValue<bool>("discardTransparent", subMesh.isAlphaTest);
            material->SetUniformValue<glm::vec4>("mainColor", glm::vec4(1.0f, 1.0f, 1.0f, 1.0f)); // Default color
           
            //add the material to the list, but check if the texture already exists in the materials, if it does, get that id and set it to this index
            bool found = false;
            int indexFound = -1;
            for (size_t i = 0; i < m_pMaterials.size(); i++)
            {
                if (m_pMaterials[i]->GetUniformValue<int>("albedo") == textureID)
                {
                    indexFound = i;
                    found = true;
                    break;
                }
            }
            if (!found)
            {
                m_pMaterials.push_back(material);
                //printf("MeshRenderer::BuildMaterialList() - New material created with texture ID %d at index %d\n", textureID, index);
            }
            else
            {
                // If the material already exists, just use that index push back but use the textureID
                //material = ;
                m_pMaterials.push_back(m_pMaterials[indexFound]);
                //printf("MeshRenderer::BuildMaterialList() - Material already exists, using existing material at index %d\n", indexFound);
                //printf("MeshRenderer::BuildMaterialList() - Material texture ID %d at index %d\n", textureID, index);
            }

        }
        index++;
    }
}