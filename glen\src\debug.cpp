#include "debug.h"

// #include <GLFW/glfw3.h>
// #include <glad/gl.h>
// #include <glm/glm.hpp>
#include <iostream>
#include <types.h>
#ifdef _WIN32
#include <windows.h>
#endif

static void std_error_callback(int error, const char *description)
{
    std::cerr << "Error: " << description << std::endl;
}

static void setColor(int textColor, int backgroundColor)
{
#ifdef _WIN32
    HANDLE hConsole = GetStdHandle(STD_OUTPUT_HANDLE);
    if (hConsole != INVALID_HANDLE_VALUE)
    {
        SetConsoleTextAttribute(hConsole, (backgroundColor << 4) | textColor);
    }
#else
    // ANSI escape codes for Linux
    std::cout << "\033[" << textColor << "m";
    std::cout << "\033[" << backgroundColor + 10 << "m";
#endif
}

void Debug::Log(std::string message, const uint32_t consoleColor)
{
    setColor(consoleColor, 0);
    std::cout << message << std::endl;
}

void Debug::Log(const ReturnTypes &error, std::string message)
{
    std::string outputString = "[";

    switch (error)
    {
    case ReturnTypes::GENERALERROR:
        setColor(RED, 0);
        outputString += "ERROR";
        break;
    case ReturnTypes::SUCCESS:
        setColor(LIGHTGREEN, 0);
        outputString += "SUCCESS";
        break;
    case ReturnTypes::SHADER_EXISTS:
        setColor(LIGHTRED, 0);
        outputString += "SHADER EXISTS";
        break;
    case ReturnTypes::SHADER_NOT_FOUND:
        setColor(RED, 0);
        outputString += "SHADER NOT FOUND";
        break;
    case ReturnTypes::SHADER_COMPILED:
        setColor(LIGHTGREEN, 0);
        outputString += "SHADER COMPILED";
        break;
    default:
        setColor(YELLOW, 0);
        outputString += "UNKNOWN";
        break;
    }
    outputString += "] ";

    std::cout << outputString;

    setColor(WHITE, 0);
    std::cout << message << std::endl;
}