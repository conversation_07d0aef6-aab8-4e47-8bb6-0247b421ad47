#version 330 core

in vec2 uv;
out vec4 FragColor;

uniform mat4 invProjection;
uniform mat4 invView;
uniform vec3 sunColor;
uniform float sunIntensity;
uniform float azimuth;   // radians
uniform float elevation; // radians
uniform float timeOfDay; // 0.0 = midnight, 0.5 = noon, 1.0 = next midnight
uniform float aspectRatio; // width / height
uniform float fov;

void main()
{
    // Map uv to NDC: (0,0) -> (-1,-1), (1,1) -> (1,1)
    vec2 ndc = uv * 2.0 - 1.0;
    ndc.y = -ndc.y;

    // Mirror pitch by flipping the y component of the view direction
    vec4 clip = vec4(ndc, 1.0, 1.0);

    // Reconstruct view space direction
    vec4 view = invProjection * clip;
    vec3 viewDir = normalize(view.xyz);
    viewDir.y = -viewDir.y; // Mirror pitch

    // Reconstruct world direction
    vec3 worldDir = normalize((invView * vec4(viewDir, 0.0)).xyz);

    // Calculate sun direction from azimuth and elevation
    vec3 sunDir = vec3(
        cos(elevation) * sin(azimuth),
        sin(elevation),
        cos(elevation) * cos(azimuth)
    );

    // Time-of-day sky color blending
    vec3 dawnColor    = vec3(0.9, 0.5, 0.3);
    vec3 dayHorizon   = vec3(0.6, 0.7, 0.9);
    vec3 dayZenith    = vec3(0.1, 0.2, 0.4);
    vec3 duskColor    = vec3(0.9, 0.4, 0.3);
    vec3 nightHorizon = vec3(0.08, 0.10, 0.18);
    vec3 nightZenith  = vec3(0.02, 0.04, 0.10);
    vec3 groundColor  = vec3(0.35, 0.32, 0.28);

    float dayAmount = smoothstep(0.20, 0.30, abs(timeOfDay - 0.5));
    float dawnDuskAmount = 1.0 - smoothstep(0.15, 0.25, abs(timeOfDay - 0.5));

    vec3 horizonColor = mix(
        mix(dayHorizon, dawnColor, dawnDuskAmount),
        nightHorizon, dayAmount
    );
    vec3 zenithColor = mix(
        mix(dayZenith, duskColor, dawnDuskAmount),
        nightZenith, dayAmount
    );

    float t = clamp(worldDir.y * 0.5 + 0.5, 0.0, 1.0);

    vec3 skyColor = mix(groundColor, mix(horizonColor, zenithColor, t), smoothstep(0.0, 0.05, worldDir.y));

    // --- Atmospheric effects ---
    float scatter = pow(1.0 - max(worldDir.y, 0.0), 2.0);
    vec3 scatterColor = vec3(0.4, 0.6, 1.0) * scatter * 0.5;

    float haze = smoothstep(0.0, 0.2, worldDir.y);
    vec3 hazeColor = vec3(0.8, 0.8, 0.7) * (1.0 - haze) * 0.2;

    // Sun disk and halo
    float sunAmount = max(dot(worldDir, sunDir), 0.0);
    float sunDisk = pow(sunAmount, 500.0);
    float sunHalo = pow(sunAmount, 10.0);
    vec3 sunGlow = sunColor * (sunDisk * 2.0 + sunHalo * 0.2) * sunIntensity;

    // ...existing code...

    // --- Lens flare (simple) ---
    // Project sunDir to screen space
    vec3 sunView = (invView * vec4(sunDir, 0.0)).xyz;
    sunView = normalize(sunView);

    // Only show lens flare when sun is in front of the camera
    float sunVisible = step(0.0, sunView.z);

    // Project to NDC using FOV and aspect ratio
    float fovRad = radians(fov);
    float tanHalfFov = tan(fovRad * 0.5);
    vec3 sunViewNdc;
    sunViewNdc.x = sunView.x / (tanHalfFov * aspectRatio);
    sunViewNdc.y = sunView.y / tanHalfFov;
    sunViewNdc.z = sunView.z;

    // Project to screen space
    vec2 sunScreen = sunViewNdc.xy * 0.5 + 0.5; // [0,1] range

    // Distance from current pixel to sun in screen space
    float flareDist = distance(uv, sunScreen);

    // Chromatic aberration for lens flare ghosts
    float chromaShift = flareDist * 0.04;
    vec3 flareColor =
        sunColor * 0.6 * exp(-pow(flareDist * 8.0, 2.0)) + // main flare
        vec3(0.8, 0.6, 1.0) * 0.2 * exp(-pow((distance(uv + chromaShift, 1.0 - sunScreen)) * 10.0, 2.0)) + // ghost 1 (shifted)
        vec3(1.0, 0.8, 0.4) * 0.1 * exp(-pow((distance(uv - chromaShift, vec2(0.5))) * 6.0, 2.0)); // ghost 2 (shifted)

    // Mask flareColor so it only appears when sun is in front
    flareColor *= sunVisible;

    // Add subtle color fringing to the sun halo itself
    vec3 sunHaloChroma = vec3(
        pow(sunAmount, 8.0),
        pow(sunAmount, 10.0),
        pow(sunAmount, 12.0)
    ) * 0.1 * sunIntensity;

    // Final color
    vec3 finalColor = skyColor + sunGlow + scatterColor + hazeColor + flareColor + sunHaloChroma;
    FragColor = vec4(finalColor, 1.0);
}