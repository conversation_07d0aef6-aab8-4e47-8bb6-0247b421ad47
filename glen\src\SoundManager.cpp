#include "SoundManager.h"
#include <fstream>
#include "debug.h"

#ifndef AL_FORMAT_MONO_FLOAT32
#define AL_FORMAT_MONO_FLOAT32 0x10010
#endif
#ifndef AL_FORMAT_STEREO_FLOAT32
#define AL_FORMAT_STEREO_FLOAT32 0x10011
#endif

#define MINIMP3_IMPLEMENTATION
#include <minimp3.h>
#include <minimp3_ex.h>

SoundManager::SoundManager()
{

    #ifdef _WIN32
    HRESULT hr = CoInitializeEx(nullptr, COINIT_MULTITHREADED);
    if (SUCCEEDED(hr)) {
        Debug::Log(ReturnTypes::SUCCESS, "COM initialized successfully");

        m_pDevice = alcOpenDevice(nullptr);

        CoUninitialize();
    }
    else
    {
        putenv("ALSOFT_DRIVERS=dsound"); //Fallback to dsound
        m_pDevice = alcOpenDevice(nullptr);
    }
    #else
    m_pDevice = alcOpenDevice(nullptr);
    #endif

    if (!m_pDevice)
    {
        Debug::Log(ReturnTypes::GENERALERROR, "Failed to open OpenAL device");
        return;
    }

    Debug::Log(ReturnTypes::SUCCESS, "OpenAL device opened successfully");

    m_pContext = alcCreateContext(m_pDevice, nullptr);
    if (!m_pContext || !alcMakeContextCurrent(m_pContext))
    {
        Debug::Log(ReturnTypes::GENERALERROR, "Failed to create OpenAL context");
        if (m_pContext)
            alcDestroyContext(m_pContext);
        alcCloseDevice(m_pDevice);
        m_pDevice = nullptr;
        m_pContext = nullptr;
    }

    #ifdef _DEBUG
    Debug::Log(ReturnTypes::SUCCESS, "OpenAL device and context created successfully");
    #endif
}

SoundManager::~SoundManager()
{
    Cleanup();
    if (m_pContext)
    {
        alcMakeContextCurrent(nullptr);
        alcDestroyContext(m_pContext);
    }
    if (m_pDevice)
    {
        alcCloseDevice(m_pDevice);
    }
}

/// @brief Loads an audio file into the sound manager
/// @param name The name of the sound
/// @param filename The path to the audio file
/// @param forceMono Whether to force the sound to be mono
/// @return True if the sound was loaded successfully, false otherwise
/// @note Supported formats: WAV (PCM, IEEE_FLOAT, MP1, MP2, MP3), MP3
bool SoundManager::LoadAudioFile(const std::string &name, const std::string &filename, bool forceMono)
{
    ALenum format;
    std::vector<char> data;
    ALsizei freq;
    int nNumChannels = 0;
    int nBitsPerSample = 0;

    if (filename.size() > 4 && filename.substr(filename.size() - 4) == ".mp3")
    {
        nBitsPerSample = 16; // minimp3 always outputs 16-bit PCM

        if (!LoadMP3File(filename, format, data, freq, nNumChannels, nBitsPerSample, forceMono))
        {
            Debug::Log(ReturnTypes::GENERALERROR, "Failed to load MP3 file: " + filename);
            return false;
        }

        SoundData sound;
        alGenBuffers(1, &sound.buffer);
        alBufferData(sound.buffer, format, data.data(), static_cast<ALsizei>(data.size()), freq);

        alGenSources(1, &sound.source);
        alSourcei(sound.source, AL_BUFFER, sound.buffer);

        m_vSounds[name] = sound;
        return true;
    }

    
    if (!LoadWAVFile(filename, format, data, freq, nNumChannels, nBitsPerSample))
    {
        std::cerr << "Failed to load WAV file: " << filename << "\n";
        return false;
    }

    // Convert stereo to mono if needed (for spatialization or if forceMono is true)
    if ((forceMono && nNumChannels == 2) || (nNumChannels == 2 && (format == AL_FORMAT_STEREO16 || format == AL_FORMAT_STEREO8)))
    {
        if (nBitsPerSample == 16)
        {
            // 16-bit stereo to mono
            size_t sampleCount = data.size() / 2; // 2 bytes per sample
            std::vector<char> monoData;
            monoData.reserve(data.size() / 2);
            for (size_t i = 0; i < sampleCount; i += 2)
            {
                int16_t left = *reinterpret_cast<int16_t *>(&data[i * 2]);
                int16_t right = *reinterpret_cast<int16_t *>(&data[i * 2 + 2]);
                int16_t mono = static_cast<int16_t>((left + right) / 2);
                monoData.push_back(static_cast<char>(mono & 0xFF));
                monoData.push_back(static_cast<char>((mono >> 8) & 0xFF));
            }
            data = std::move(monoData);
            format = AL_FORMAT_MONO16;
        }
        else if (nBitsPerSample == 8)
        {
            // 8-bit stereo to mono
            std::vector<char> monoData;
            monoData.reserve(data.size() / 2);
            for (size_t i = 0; i < data.size(); i += 2)
            {
                uint8_t left = static_cast<uint8_t>(data[i]);
                uint8_t right = static_cast<uint8_t>(data[i + 1]);
                uint8_t mono = static_cast<uint8_t>((left + right) / 2);
                monoData.push_back(static_cast<char>(mono));
            }
            data = std::move(monoData);
            format = AL_FORMAT_MONO8;
        }
    }

    SoundData sound;
    alGenBuffers(1, &sound.buffer);
    alBufferData(sound.buffer, format, data.data(), static_cast<ALsizei>(data.size()), freq);

    alGenSources(1, &sound.source);
    alSourcei(sound.source, AL_BUFFER, sound.buffer);

    m_vSounds[name] = sound;
    return true;
}

/// @brief Plays a sound source
/// @param soundProperties The sound properties to play
void SoundManager::Play(const SoundProperties &soundProperties)
{
    auto it = m_vSounds.find(soundProperties.name);
    if (it != m_vSounds.end())
    {
        alSourcei(it->second.source, AL_LOOPING, soundProperties.Loop ? AL_TRUE : AL_FALSE);
        alSourcei(it->second.source, AL_SOURCE_RELATIVE, AL_TRUE); // Non-positional
        alSource3f(it->second.source, AL_POSITION, 0.0f, 0.0f, 0.0f); // Centered
        alSourcef(it->second.source, AL_GAIN, soundProperties.Volume / 100.0f);
        alSourcef(it->second.source, AL_PITCH, soundProperties.Pitch);
        alSourcePlay(it->second.source);
    }
}

/// @brief Plays a sound at a specific position in world space
/// @param soundProperties The sound properties to play
/// @param position The position in world space
void SoundManager::PlayAt(const SoundProperties &soundProperties, const glm::vec3 &position)
{
    auto it = m_vSounds.find(soundProperties.name);
    if (it != m_vSounds.end())
    {
        alSourcei(it->second.source, AL_SOURCE_RELATIVE, AL_FALSE);
        alSourcei(it->second.source, AL_LOOPING, soundProperties.Loop ? AL_TRUE : AL_FALSE);
        alSource3f(it->second.source, AL_POSITION, position.x, position.y, position.z);
        alSourcef(it->second.source, AL_REFERENCE_DISTANCE, 1.0f);
        alSourcef(it->second.source, AL_MAX_DISTANCE, 10.0f);
        alSourcef(it->second.source, AL_ROLLOFF_FACTOR, 1.0f);
        alSourcePlay(it->second.source);
    }
}

/// @brief Stops a sound source
/// @param soundProperties The sound properties to stop
void SoundManager::Stop(const SoundProperties &soundProperties)
{
    auto it = m_vSounds.find(soundProperties.name);
    if (it != m_vSounds.end())
    {
        alSourceStop(it->second.source);
    }
}

/// @brief Sets the listener position and orientation
/// @param position The position of the listener
/// @param forward The forward direction of the listener
/// @param up The up direction of the listener
void SoundManager::SetListenerPosition(const glm::vec3 &position, const glm::vec3 &forward, const glm::vec3 &up)
{
    float orientation[6] = {
        forward.x, forward.y, forward.z,
        up.x, up.y, up.z};
    alListenerfv(AL_ORIENTATION, orientation);
    alListener3f(AL_POSITION, position.x, position.y, position.z);
}

/// @brief Updates the position of a sound source and sets it to be non-positional if sound is not 3D
/// @param soundProperties The sound properties to update
/// @param pos The new position of the sound source
void SoundManager::UpdateSoundPosition(const SoundProperties &soundProperties, const glm::vec3 &pos)
{
    auto it = m_vSounds.find(soundProperties.name);
    if (it != m_vSounds.end())
    {
        if(!soundProperties.Is3D)
        {
            alSourcei(it->second.source, AL_SOURCE_RELATIVE, AL_TRUE);
            alSource3f(it->second.source, AL_POSITION, 0.0f, 0.0f, 0.0f); // Centered

        }
        else
        {
            alSource3f(it->second.source, AL_POSITION, pos.x, pos.y, pos.z);
        }
    }
}

/// @brief Updates the 3D position of a sound source
/// @param soundProperties The sound properties to update
void SoundManager::UpdateSoundData(const SoundProperties &soundProperties)
{
    auto it = m_vSounds.find(soundProperties.name);
    if (it != m_vSounds.end())
    {
        alSourcef(it->second.source, AL_GAIN, soundProperties.Volume / 100.0f);
        alSourcef(it->second.source, AL_PITCH, soundProperties.Pitch);
        alSourcef(it->second.source, AL_REFERENCE_DISTANCE, soundProperties.MinDistance);
        alSourcef(it->second.source, AL_MAX_DISTANCE, soundProperties.MaxDistance);
        alSourcef(it->second.source, AL_ROLLOFF_FACTOR, soundProperties.RolloffFactor);
    }
}

void SoundManager::Cleanup()
{
    for (auto &pair : m_vSounds)
    {
        alDeleteSources(1, &pair.second.source);
        alDeleteBuffers(1, &pair.second.buffer);
    }
    m_vSounds.clear();
}

/// @brief Loads a WAV file
/// @param filename The path to the WAV file
/// @param format The audio format (output)
/// @param data The audio data (output)
/// @param freq The sample frequency (output)
/// @param numChannels The number of channels (output)
/// @param bitsPerSample The bits per sample (output)
/// @return True if successful, false otherwise
bool SoundManager::LoadWAVFile(const std::string &filename, ALenum &format, std::vector<char> &data, ALsizei &freq, int &numChannels, int &bitsPerSample)
{
    std::ifstream file(filename, std::ios::binary);
    if (!file)
        return false;

    std::array<char, 4> riff;
    file.read(riff.data(), 4);
    if (std::string(riff.data(), 4) != "RIFF")
        return false;
    file.ignore(4); // Skip file size
    std::array<char, 4> wave;
    file.read(wave.data(), 4);
    if (std::string(wave.data(), 4) != "WAVE")
        return false;

    AudioFormat audioFormat = AudioFormat::UNKNOWN;
    int32_t nSampleRate = 0;
    bool bFormatFound = false, bDataFound = false;
    int nFormatChunkSize = 0;
    std::vector<char> soundData;

    while (!file.eof())
    {
        std::array<char, 4> chunkId;
        int32_t nChunkSize = 0;
        file.read(chunkId.data(), 4);
        file.read(reinterpret_cast<char *>(&nChunkSize), 4);
        if (file.gcount() < 4)
            break;

        if (std::string(chunkId.data(), 4) == "fmt ")
        {
            bFormatFound = true;
            file.read(reinterpret_cast<char *>(&audioFormat), 2);
            file.read(reinterpret_cast<char *>(&numChannels), 2);
            file.read(reinterpret_cast<char *>(&nSampleRate), 4);
            file.ignore(6); // Skip byteRate and blockAlign
            file.read(reinterpret_cast<char *>(&bitsPerSample), 2);
            nFormatChunkSize = nChunkSize;
            file.ignore(nChunkSize - 16); // Skip the rest
        }
        else if (std::string(chunkId.data(), 4) == "data")
        {
            bDataFound = true;
            soundData.resize(nChunkSize);
            file.read(soundData.data(), nChunkSize);
        }
        else
        {
            file.ignore(nChunkSize);
        }
        if (bFormatFound && bDataFound)
            break;
    }

    if (!bFormatFound || !bDataFound)
        return false;

    freq = nSampleRate;

    if (audioFormat == AudioFormat::MP3)
    {
        mp3dec_ex_t minimp3Decoder;

        if (mp3dec_ex_open_buf(&minimp3Decoder, reinterpret_cast<const uint8_t*>(soundData.data()), soundData.size(), MP3D_SEEK_TO_SAMPLE) != 0)
            return false;

        freq = minimp3Decoder.info.hz;
        numChannels = minimp3Decoder.info.channels;
        bitsPerSample = 16; // minimp3 always outputs 16-bit PCM

        size_t nSampleCount = minimp3Decoder.samples;
        std::vector<mp3d_sample_t> pcmSamples(nSampleCount);
        mp3dec_ex_read(&minimp3Decoder, pcmSamples.data(), nSampleCount);

        // Convert to mono if needed
        if (numChannels == 2)
        {
            std::vector<mp3d_sample_t> monoPcmSamples(nSampleCount / 2);
            for (size_t i = 0, j = 0; i + 1 < nSampleCount; i += 2, ++j)
            {
                monoPcmSamples[j] = (pcmSamples[i] + pcmSamples[i + 1]) / 2;
            }
            pcmSamples = std::move(monoPcmSamples);
            numChannels = 1;
        }

        // Fill OpenAL format
        format = (numChannels == 1) ? AL_FORMAT_MONO16 : AL_FORMAT_STEREO16;

        // Copy to char buffer for OpenAL
        data.resize(pcmSamples.size() * sizeof(mp3d_sample_t));
        memcpy(data.data(), pcmSamples.data(), data.size());

        mp3dec_ex_close(&minimp3Decoder);
        return true;
    }

    if (audioFormat == AudioFormat::PCM)
    {
        if (numChannels == 1)
        {
            format = (bitsPerSample == 8) ? AL_FORMAT_MONO8 : (bitsPerSample == 16) ? AL_FORMAT_MONO16
                                                                                    : 0;
        }
        else if (numChannels == 2)
        {
            format = (bitsPerSample == 8) ? AL_FORMAT_STEREO8 : (bitsPerSample == 16) ? AL_FORMAT_STEREO16
                                                                                      : 0;
        }
        else
        {
            return false; // Unsupported channel count
        }
        data = std::move(soundData); //PCM data in this case
    }
    else if (audioFormat == AudioFormat::IEEE_FLOAT)
    {
        if (bitsPerSample == 32)
        {
            if (numChannels == 1)
                format = AL_FORMAT_MONO_FLOAT32;
            else if (numChannels == 2)
                format = AL_FORMAT_STEREO_FLOAT32;
            else
                return false;
        }
        else
        {
            return false; // Only 32-bit float supported
        }
        data = std::move(soundData); // float PCM data in this case
    }
    else
    {
        return false; // Unsupported format
    }
    if (format == 0)
        return false;
    return true;
}

/// @brief Loads an MP3 file
/// @param filename The path to the MP3 file
/// @param format The audio format (output)
/// @param data The audio data (output)
/// @param freq The sample frequency (output)
/// @param numChannels The number of channels (output)
/// @param bitsPerSample The bits per sample (output)
/// @param forceMono Whether to force the sound to be mono
/// @return True if successful, false otherwise
bool SoundManager::LoadMP3File(const std::string &filename, ALenum &format, std::vector<char> &data, ALsizei &freq, int &numChannels, int &bitsPerSample, bool forceMono)
{
    mp3dec_ex_t minimp3Decoder;
    if (mp3dec_ex_open(&minimp3Decoder, filename.c_str(), MP3D_SEEK_TO_SAMPLE) != 0)
        return false;

    freq = minimp3Decoder.info.hz;
    numChannels = minimp3Decoder.info.channels;
    bitsPerSample = 16; // minimp3 always outputs 16-bit PCM

    size_t nSampleCount = minimp3Decoder.samples;
    std::vector<mp3d_sample_t> pcmSamples(nSampleCount);
    mp3dec_ex_read(&minimp3Decoder, pcmSamples.data(), nSampleCount);

    // Convert to mono if needed
    if ((forceMono && numChannels == 2) || numChannels == 2)
    {
        std::vector<mp3d_sample_t> monoPcmSamples(nSampleCount / 2);
        for (size_t i = 0, j = 0; i + 1 < nSampleCount; i += 2, ++j)
        {
            monoPcmSamples[j] = (pcmSamples[i] + pcmSamples[i + 1]) / 2;
        }
        pcmSamples = std::move(monoPcmSamples);
        numChannels = 1;
    }

    // Fill OpenAL format
    format = (numChannels == 1) ? AL_FORMAT_MONO16 : AL_FORMAT_STEREO16;

    // Copy to char buffer for OpenAL
    data.resize(pcmSamples.size() * sizeof(mp3d_sample_t));
    memcpy(data.data(), pcmSamples.data(), data.size());

    mp3dec_ex_close(&minimp3Decoder);
    return true;
}