#ifndef INPUT_MANAGER_H
#define INPUT_MANAGER_H

#include <GLFW/glfw3.h>
#include <unordered_map>
#include <glm/vec2.hpp> // Include GLM for vec2
#include <iostream>     // Include for std::cout and std::endl

class InputManager
{
public:
    static InputManager &GetInstance()
    {
        static InputManager instance;
        return instance;
    }

    void Initialize(GLFWwindow *window)
    {
        m_Window = window;
        glfwSetKeyCallback(window, KeyCallback);
        glfwSetMouseButtonCallback(window, MouseButtonCallback);
        glfwSetCursorPosCallback(window, CursorPosCallback);
        for (int i = 0; i < 1024; ++i)
        {
            m_KeyStates[i] = {false, false};
        }
        for (int i = 0; i < 3; ++i)
        {
            m_MouseButtonStates[i] = {false, false};
        }
        m_MousePosition = {0.0, 0.0};
    }

    static void Update()
    {
        auto &instance = GetInstance();
        for (auto &[key, state] : instance.m_KeyStates)
        {
            state.previous = state.current;
            auto &instance = GetInstance();
            for (auto &[button, state] : instance.m_MouseButtonStates)
            {
                state.previous = state.current;
            }
        }

        if(!instance.m_bCursorEnabled)
        {
            //hide the cursor and lock it to the window
            glfwSetInputMode(instance.m_Window, GLFW_CURSOR, GLFW_CURSOR_DISABLED);
        }
        else
        {
            //show the cursor and unlock it from the window
            glfwSetInputMode(instance.m_Window, GLFW_CURSOR, GLFW_CURSOR_NORMAL);
        }
    }

    void EnableCursor(bool enable)
    {
        m_bCursorEnabled = enable;

        if (m_bCursorEnabled)
        {
            glfwSetInputMode(m_Window, GLFW_CURSOR, GLFW_CURSOR_NORMAL);
        }
        else
        {
            glfwSetInputMode(m_Window, GLFW_CURSOR, GLFW_CURSOR_DISABLED);
        }
    }

    bool IsKeyPressed(int key) const
    {
        auto it = m_KeyStates.find(key);
        if (it == m_KeyStates.end())
        {
            return false; // Key not found
        }
        return it->second.current && !it->second.previous;
    }

    bool IsKeyDown(int key) const
    {
        return m_KeyStates.at(key).current;
    }

    bool IsKeyReleased(int key) const
    {
        return !m_KeyStates.at(key).current && m_KeyStates.at(key).previous;
    }

    bool IsMouseButtonPressed(int button) const
    {
        return m_MouseButtonStates.at(button).current && !m_MouseButtonStates.at(button).previous;
    }

    bool IsMouseButtonDown(int button) const
    {
        return m_MouseButtonStates.at(button).current;
    }

    bool IsMouseButtonReleased(int button) const
    {
        return !m_MouseButtonStates.at(button).current && m_MouseButtonStates.at(button).previous;
    }
    double GetMouseX() const
    {
        return m_MousePosition.x;
    }
    double GetMouseY() const
    {
        return m_MousePosition.y;
    }
    glm::vec2 GetMousePosition() const
    {
        return {m_MousePosition.x, m_MousePosition.y};
    }
    glm::vec2 GetMousePositionChange() const
    {
        return {m_MousePositionChange.x, m_MousePositionChange.y};
    }
    void SetMousePosition(double x, double y)
    {
        m_MousePosition.x = x;
        m_MousePosition.y = y;
        m_MousePositionChange = {x - m_MousePosition.x, y - m_MousePosition.y};
        glfwSetCursorPos(m_Window, x, y);
    }

private:
    struct ButtonState
    {
        bool current = false;
        bool previous = false;
    };

    struct MousePosition
    {
        double x = 0.0;
        double y = 0.0;
    };

    MousePosition m_MousePosition;
    MousePosition m_MousePositionChange;

    bool m_bCursorEnabled = true;

    static void CursorPosCallback(GLFWwindow *window, double xpos, double ypos)
    {
        auto &instance = GetInstance();
        // Calculate the delta first
        instance.m_MousePositionChange.x = xpos - instance.m_MousePosition.x;
        instance.m_MousePositionChange.y = ypos - instance.m_MousePosition.y;

        // Update the current mouse position
        instance.m_MousePosition.x = xpos;
        instance.m_MousePosition.y = ypos;
    }

    GLFWwindow *m_Window = nullptr;
    std::unordered_map<int, ButtonState> m_KeyStates;
    std::unordered_map<int, ButtonState> m_MouseButtonStates;

    InputManager() = default;

    static void KeyCallback(GLFWwindow *window, int key, int scancode, int action, int mods)
    {
        auto &instance = GetInstance();
        if (action == GLFW_PRESS)
        {
            instance.m_KeyStates[key].current = true;
            // std::cout << "Key " << key << " pressed" << std::endl;
        }
        else if (action == GLFW_RELEASE)
        {
            instance.m_KeyStates[key].current = false;
            // std::cout << "Key " << key << " released" << std::endl;
        }
    }

    static void MouseButtonCallback(GLFWwindow *window, int button, int action, int mods)
    {
        auto &instance = GetInstance();
        if (action == GLFW_PRESS)
        {
            instance.m_MouseButtonStates[button].current = true;
        }
        else if (action == GLFW_RELEASE)
        {
            instance.m_MouseButtonStates[button].current = false;
        }
    }
};

#endif // INPUT_MANAGER_H