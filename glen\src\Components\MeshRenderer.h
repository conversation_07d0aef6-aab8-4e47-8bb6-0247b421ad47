#pragma once

#include <Component.h>
#include <Renderer/Mesh.h>
#include <Renderer/Material.h>
#include <Renderer/ShadowUtils.h>
#include <Components/Camera.h>
#include <Components/Light.h>

class MeshRenderer : public Component, public std::enable_shared_from_this<MeshRenderer>
{
public:
    Mesh<PERSON>enderer();
    MeshRenderer(std::shared_ptr<Mesh> mesh);
    ~MeshRenderer();
    void Render(Camera* camera);
    void BlurShadowMapPingPong();
    void RenderDepth(glm::vec3 lightLookAtPos, glm::vec3 lightPos, Camera* camera);
    void RenderDirectionalDepth(Light* directionalLight, Camera* camera);
    void RenderPointDepth(Light* light);
    void RenderPointLights(std::vector<Light*> lights);
    void BlurShadowMap(GLuint inputTex, GLuint outputTex, GLuint blurShader, bool horizontal);

    void Update() override;
    void Start() override;

    void SetMesh(std::unique_ptr<Mesh> mesh) { this->m_pMesh = std::move(mesh); }

    std::shared_ptr<Mesh> GetMesh() { return m_pMesh; }

    void AddMaterial(std::shared_ptr<Material> material)
    {
        m_pMaterials.push_back(material);
    }
    std::vector<std::shared_ptr<Material>> GetMaterials() { return m_pMaterials; }
    //set material at index
    void SetMaterialAtIndex(std::shared_ptr<Material> material, size_t index)
    {
        if (index < m_pMaterials.size())
        {
            m_pMaterials[index] = material;
        }
        else
        {
            m_pMaterials.push_back(material);
        }
    }

    std::shared_ptr<Material> GetMaterialAtIndex(size_t index)
    {
        if (index < m_pMaterials.size())
        {
            return m_pMaterials[index];
        }
        return nullptr; // or throw an exception
    }

    void BuildMaterialList();

    std::string GetUUID() { return m_UUID; }
    AABB GetBounds() { return m_pMesh->bounds; }

    void UpdateMesh();

    std::vector<BuiltInFieldInfo> GetBuiltInFields() override
    {
        std::vector<BuiltInFieldInfo> fields;
        fields.reserve(3);
        fields.push_back(BuiltInFieldInfo{"Enabled", "Enabled", &m_bEnabled});
        fields.push_back(BuiltInFieldInfo{"Mesh", "Mesh", &m_pMesh});
        //fields.push_back(BuiltInFieldInfo{"Material", "Material", &m_pMaterial});
        return fields;
    }

    private:
    std::shared_ptr<Mesh> m_pMesh;
    std::vector<std::shared_ptr<Material>> m_pMaterials = {};
    std::string m_UUID;
    bool m_bEnabled = true; // Whether the MeshRenderer is enabled or not

};