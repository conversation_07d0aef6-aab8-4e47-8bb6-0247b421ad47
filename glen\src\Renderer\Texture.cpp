#include "Texture.h"
#include "TextureManager.h"
#include <debug.h>
#include <glad/gl.h>
#include <GLFW/glfw3.h>

#include <Renderer/TextureFormats/Dtx.h>
#define STB_IMAGE_IMPLEMENTATION
#include "../vendor/thirdparty/stb_image.h"

Texture::TextureFileType Texture::GetFileTypeFromFile(const std::string &fileName) const
{

    std::string extension = fileName.substr(fileName.find_last_of(".") + 1);
    if (extension == "dtx") return Texture::TextureFileType::DTX;
    if (extension == "dds") return Texture::TextureFileType::DDS;
    if (extension == "png") return Texture::TextureFileType::PNG;
    if (extension == "jpg") return Texture::TextureFileType::JPG;
    if (extension == "bmp") return Texture::TextureFileType::BMP;
    if (extension == "tga") return Texture::TextureFileType::TGA;

    return Texture::TextureFileType();
}

void Texture::SetName(const std::string &name)
{
    m_name = name;
}
const std::string &Texture::GetName() const
{
    return m_name;
}
void Texture::SetWidth(int width)
{
    m_width = width;
}
int Texture::GetWidth() const
{
    return m_width;
}
void Texture::SetHeight(int height)
{
    m_height = height;
}
int Texture::GetHeight() const
{
    return m_height;
}
void Texture::SetFormat(int format)
{
    m_format = static_cast<TextureFormat>(format);
}
int Texture::GetFormat() const
{
    return static_cast<int>(m_format);
}
void Texture::SetData(const unsigned char *data, size_t size)
{
    m_data = new unsigned char[size];
    memcpy(m_data, data, size);
    m_size = size;
}
const unsigned char *Texture::GetData() const
{
    return m_data;
}

void Texture::Bind(int unit)
{
    glActiveTexture(GL_TEXTURE0 + unit);
    glBindTexture(GL_TEXTURE_2D, m_nTextureID);
}

void Texture::Unbind()
{
    glBindTexture(GL_TEXTURE_2D, 0);
}

void Texture::SetTextureFormat(TextureFormat format)
{
    m_format = format;
}
void Texture::SetTextureType(TextureType type)
{
    m_type = type;
}

void Texture::SetStorageType(TextureFileType type)
{
    m_storageType = type;
}
void Texture::SetTextureID(unsigned int textureID)
{
    m_nTextureID = textureID;
}
unsigned int Texture::GetTextureID() const
{
    return m_nTextureID;
}
Texture::TextureFormat Texture::GetTextureFormat() const
{
    return m_format;
}
Texture::TextureType Texture::GetTextureType() const
{
    return m_type;
}
Texture::TextureFileType Texture::GetFileType() const
{
    return m_storageType;
}

void Texture::SetChannels(int channels)
{
    m_channels = channels;
}
int Texture::GetChannels() const
{
    return m_channels;
}

bool Texture::LoadTGA(const std::string &fileName)
{
    // Load TGA file
    // Implement TGA loading logic here
    return true;
}
bool Texture::LoadPNG(const std::string &fileName)
{
    int width, height, channels;

    stbi_set_flip_vertically_on_load(true);

    unsigned char* data = stbi_load(fileName.c_str(), &width, &height, &channels, 4); // Force RGBA

    if (!data)
    {
        Debug::Log(ReturnTypes::GENERALERROR, "Failed to load PNG: " + fileName);
        return false;
    }
    
    SetWidth(width);
    SetHeight(height);
    SetChannels(4);
    SetData(data, width * height * 4);
    SetTextureFormat(TextureFormat::RGBA);
    SetName(fileName);
    

    glGenTextures(1, &m_nTextureID);
    SetTextureID(m_nTextureID);
    
    glBindTexture(GL_TEXTURE_2D, m_nTextureID);
    glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, width, height, 0, GL_RGBA, GL_UNSIGNED_BYTE, data);
    glGenerateMipmap(GL_TEXTURE_2D);
    
    stbi_image_free(data);
    return true;
}
bool Texture::LoadJPG(const std::string &fileName)
{
    int width, height, channels;
    stbi_set_flip_vertically_on_load(true);
    unsigned char* data = stbi_load(fileName.c_str(), &width, &height, &channels, 4); // Force RGBA
    
    if (!data)
    {
        Debug::Log(ReturnTypes::GENERALERROR, "Failed to load JPG: " + fileName);
        return false;
    }
    
    SetWidth(width);
    SetHeight(height);
    SetChannels(4);
    SetData(data, width * height * 4);
    SetTextureFormat(TextureFormat::RGBA);
    SetName(fileName);
    
    glGenTextures(1, &m_nTextureID);
    SetTextureID(m_nTextureID);
    
    glBindTexture(GL_TEXTURE_2D, m_nTextureID);
    glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, width, height, 0, GL_RGBA, GL_UNSIGNED_BYTE, data);
    glGenerateMipmap(GL_TEXTURE_2D);
    
    stbi_image_free(data);
    return true;
}
bool Texture::LoadBMP(const std::string &fileName)
{
    // Load BMP file
    // Implement BMP loading logic here
    return true;
}
bool Texture::LoadDDS(const std::string &fileName)
{
    // Load DDS file
    // Implement DDS loading logic here
    return true;
}
bool Texture::LoadDTX(const std::string &fileName)
{
    // Load the DTX texture using the DTX loader
    std::shared_ptr<Texture> loaded = DTX::LoadDTX(fileName);
    if (!loaded) return false;

    // Copy relevant data from loaded texture to this instance
    SetName(loaded->GetName());
    SetWidth(loaded->GetWidth());
    SetHeight(loaded->GetHeight());
    SetFormat(loaded->GetFormat());
    SetChannels(loaded->GetChannels());
    SetTextureFormat(loaded->GetTextureFormat());
    SetTextureType(loaded->GetTextureType());
    SetStorageType(loaded->GetFileType());
    SetTextureID(loaded->GetTextureID());

    // Copy texture data
    if (m_data)
    {
        delete[] m_data;
        m_data = nullptr;
        m_size = 0;
    }
    else
    {
        Debug::Log(ReturnTypes::GENERALERROR, "Failed to load DTX: " + fileName);
        m_data = nullptr;
        m_size = 0;
        return false;
    }

    if (loaded->GetData() && loaded->m_size > 0)
    {
        m_data = new unsigned char[loaded->m_size];
        memcpy(m_data, loaded->GetData(), loaded->m_size);
        m_size = loaded->m_size;
    }

    return true;
}

Texture::Texture()
    : m_width(0), m_height(0), m_format(TextureFormat::RGBA), m_type(TextureType::TEXTURE_2D),
      m_storageType(TextureFileType::PNG), m_nTextureID(0), m_data(nullptr), m_size(0)
{
    glGenTextures(1, &m_nTextureID);
}

Texture::~Texture()
{
    if (m_data)
    {
        delete[] m_data;
    }
    glDeleteTextures(1, &m_nTextureID);
}
