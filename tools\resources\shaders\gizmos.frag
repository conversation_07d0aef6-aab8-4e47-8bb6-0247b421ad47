#version 450 core
out vec4 FragColor;

uniform vec3 mainColor;
uniform bool isSolid;
uniform float alpha = 1.0; // Alpha uniform with default value of 1.0
// Set the light position to a point in the sky (e.g., above and in front of the scene)
uniform vec3 lightPos = vec3(0.0, 10.0, 10.0); // Example: sky position

in vec3 FragNormal;

void main() {
    // Normalize the normal
    vec3 normal = normalize(FragNormal);

    // Assume the fragment is at the origin (0,0,0)
    vec3 lightDir = normalize(lightPos);

    // Simple diffuse shading (Lambertian)
    float diff = max(dot(normal, lightDir), 0.0);

    // Add a small ambient term to prevent complete darkness
    float ambient = 0.35;
    float lighting = ambient + (1.0 - ambient) * diff;

    vec3 color;
    if(!isSolid)
    {
        color = mainColor;
    }
    else
    {
        // If solid, use the color modulated by lighting
        color = mainColor * lighting;
    }

    FragColor = vec4(color, alpha);
}
