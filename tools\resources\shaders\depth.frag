#version 450 core

layout(location = 0) out float FragDepth;

uniform sampler2D albedo; // Albedo texture
uniform bool discardTransparent; // Control whether to discard transparent fragments

in vec2 TexCoords;

void main()
{
    vec4 texColor = texture(albedo, TexCoords);

    // Discard the fragment if the alpha value is below a threshold
    if (texColor.a < 0.1 && discardTransparent) {
        discard;
    }

    // Output the depth value
    FragDepth = gl_FragCoord.z;
}