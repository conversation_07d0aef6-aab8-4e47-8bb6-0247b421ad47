cmake_minimum_required(VERSION 3.25)

include(FetchContent)

project(glen VERSION 0.0.1)

set(CMAKE_CXX_STANDARD 17)

set(GLEN_COMPONENTS "GLFW;GLAD;GLM;OPENAL;MINIMP3" CACHE STRING "The components to import and build")

foreach (comp IN ITEMS ${GLEN_COMPONENTS})
    set(IMPORT_${comp} ON)
endforeach ()

if(IMPORT_MINIMP3)
    FetchContent_Declare(
            minimp3
            GIT_REPOSITORY https://github.com/lieff/minimp3.git
            GIT_TAG        master
    )
    list(APPEND components minimp3)
    FetchContent_Populate(minimp3)
    include_directories(${minimp3_SOURCE_DIR})
endif ()

if (IMPORT_IMGUI)
    set(IMPORT_GLAD ON)
    set(IMPORT_GLFW ON)
endif ()

if (IMPORT_GLAD)
    option(GLEN_GLAD_DOWNLOAD "If set to ON the glad gl loader will be generated and downloaded, if OFF the included version (gl4.5/core) will be used" ON)
    if (GLEN_GLAD_DOWNLOAD)
        set(GLEN_GLAD_GL_VERSION 4.5 CACHE STRING "The target gl version")
        option(GLEN_GLAD_GL_CORE "The target gl profile. ON = core profile, OFF = compatibility profile" ON)
        if (GLEN_GLAD_GL_CORE)
            set(GLAD_GL_PROFILE core)
        else ()
            set(GLAD_GL_PROFILE compatibility)
        endif ()
    else ()
        unset(GLEN_GLAD_GL_VERSION CACHE)
        unset(GLEN_GLAD_GL_CORE CACHE)
    endif ()
else ()
    unset(GLEN_GLAD_DOWNLOAD CACHE)
    unset(GLEN_GLAD_GL_VERSION CACHE)
    unset(GLEN_GLAD_GL_CORE CACHE)
endif ()
if (IMPORT_IMGUI)
    set(GLEN_IMGUI_TAG master CACHE STRING "Defines the imgui version (e.g. master, v1.89.4, v1.89.3, ...")
    set(IMPORT_GLFW ON)
else ()
    unset(GLEN_IMGUI_TAG CACHE)
endif ()
if (IMPORT_GLFW)
    set(GLEN_GLFW_TAG master CACHE STRING "Defines the glfw version (e.g. master, 3.3.8, 3.3.7, ...) ")
else ()
    unset(GLEN_GLFW_TAG CACHE)
endif ()
if (IMPORT_GLM)
    set(GLEN_GLM_TAG master CACHE STRING "Defines the glm version (e.g. master, 0.9.9.8, 0.9.9.7, ...")
else ()
    unset(GLEN_GLM_TAG CACHE)
endif ()

##############################
#            glad            #
##############################
if (IMPORT_GLAD)
    if (NOT GLEN_GLAD_DOWNLOAD)
        message(STATUS "Using included version of the glad loader sources (gl 4.5/core) ")
        set(glad_SOURCE_DIR glad)
    else ()
        if ("${glad_INSTALLED_VERSION}" STREQUAL "${GLEN_GLAD_GL_VERSION}-${GLAD_GL_PROFILE}")
            message(STATUS "Avoiding repeated download of glad gl ${GLEN_GLAD_GL_VERSION}/${GLAD_GL_PROFILE}")
            set(glad_SOURCE_DIR ${glad_LAST_SOURCE_DIR})
        else ()
            find_program(GLEN_CURL NAMES curl curl.exe)
            if (NOT GLEN_CURL)
                message(STATUS "Could not find curl, using included version of the glad loader sources (gl 4.5/core)")
                set(glad_SOURCE_DIR glad)
            else ()
                execute_process(
                        COMMAND ${GLEN_CURL} -s -D - -X POST -d generator=c&api=egl%3Dnone&api=gl%3D${GLEN_GLAD_GL_VERSION}&profile=gl%3D${GLAD_GL_PROFILE}&api=gles1%3Dnone&profile=gles1%3Dcommon&api=gles2%3Dnone&api=glsc2%3Dnone&api=glx%3Dnone&api=vulkan%3Dnone&api=wgl%3Dnone&options=LOADER https://gen.glad.sh/generate
                        OUTPUT_VARIABLE out
                        RESULT_VARIABLE res
                        ERROR_VARIABLE err
                )
                if (NOT res EQUAL "0")
                    message(WARNING "${GLEN_CURL} returned: " ${res})
                    if (err)
                        message(WARNING "Error message: " ${err})
                    endif ()
                    message(STATUS "Using included version of the glad loader sources (gl 4.5/core)")
                    set(glad_SOURCE_DIR glad)
                else ()
                    string(REGEX MATCH "[Ll][Oo][Cc][Aa][Tt][Ii][Oo][Nn]: ([A-Za-z0-9_\\:/\\.]+)" location ${out})
                    set(location "${CMAKE_MATCH_1}")
                    if (NOT location OR location STREQUAL "/")
                        message(WARNING "Could not extract location from http response, using included version of the glad loader sources (gl 4.5/core)")
                        message(STATUS "Response: " ${out})
                        set(glad_SOURCE_DIR glad)
                    else ()
                        string(REGEX REPLACE "/$" "" location ${location})
                        string(APPEND location "/glad.zip")
                        if (NOT ${location} MATCHES "^http")
                            string(PREPEND location "https://gen.glad.sh")
                        endif ()
                        message(STATUS "Downloading glad loader sources for gl${GLEN_GLAD_GL_VERSION}/${GLAD_GL_PROFILE} from ${location}")
                        FetchContent_Declare(
                                glad
                                URL ${location}
                        )
                        FetchContent_MakeAvailable(glad)
                        set(glad_INSTALLED_VERSION ${GLEN_GLAD_GL_VERSION}-${GLAD_GL_PROFILE} CACHE INTERNAL "")
                        set(glad_LAST_SOURCE_DIR ${glad_SOURCE_DIR} CACHE INTERNAL "")
                    endif ()
                endif ()
            endif ()
        endif ()
    endif ()

    add_library(
            glad
            ${glad_SOURCE_DIR}/src/gl.c
            ${glad_SOURCE_DIR}/include/glad/gl.h
            ${glad_SOURCE_DIR}/include/KHR/khrplatform.h
    )
    target_include_directories(glad PUBLIC ${glad_SOURCE_DIR}/include)
endif ()

if (IMPORT_GLFW)
    if (NOT EMSCRIPTEN)
        FetchContent_Declare(
                glfw
                GIT_REPOSITORY https://github.com/glfw/glfw.git
                GIT_TAG ${GLEN_GLFW_TAG}
        )
        list(APPEND components glfw)
    endif ()
endif ()

if (IMPORT_GLM)
    FetchContent_Declare(
            glm
            GIT_REPOSITORY https://github.com/g-truc/glm.git
            GIT_TAG ${GLEN_GLM_TAG}
    )
    list(APPEND components glm)

    FetchContent_GetProperties(glm)

    if (NOT glm_POPULATED)
        FetchContent_Populate(glm)
        add_subdirectory(${glm_SOURCE_DIR} ${glm_BINARY_DIR})

        if (NOT TARGET glm)
            add_library(glm INTERFACE)
            target_include_directories(glm INTERFACE ${glm_SOURCE_DIR})
        endif()
    endif()
    include_directories(${glm_SOURCE_DIR})
endif ()

if(IMPORT_OPENAL)
    FetchContent_Declare(
            OpenAL
            GIT_REPOSITORY https://github.com/kcat/OpenAL-soft.git
            GIT_TAG 1.21.1
    )
    set(BUILD_EXAMPLES OFF CACHE BOOL "Disable OpenAL examples" FORCE)
    set(OPENAL_EXAMPLES OFF CACHE BOOL "Disable OpenAL examples" FORCE)
    set(ALSOFT_UTILS OFF CACHE BOOL "Disable OpenAL utility programs" FORCE)
    set(ALSOFT_EXAMPLES OFF CACHE BOOL "Disable OpenAL example programs" FORCE)
    FetchContent_MakeAvailable(OpenAL)
    list(APPEND components OpenAL)
endif()

if (components)
    FetchContent_MakeAvailable(${components})
endif ()

if(IMPORT_OPENAL)
    #link winmm
    if (WIN32)
        target_link_libraries(OpenAL PUBLIC winmm)
    endif ()
endif()

set(SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/glen/src)

file(GLOB_RECURSE HEADER_FILES ${CMAKE_CURRENT_SOURCE_DIR}/glen/src/*.h)
file(GLOB_RECURSE HEADER_FILES ${CMAKE_CURRENT_SOURCE_DIR}/glen/src/vendor/thirdparty/*.h)

set(SOURCE_FILES
${SOURCE_DIR}/Components/Transform.cpp 
${SOURCE_DIR}/Components/Light.cpp 
${SOURCE_DIR}/Components/MeshRenderer.cpp 
${SOURCE_DIR}/Components/Camera.cpp 
${SOURCE_DIR}/Renderer/Shader.cpp 
${SOURCE_DIR}/Renderer/ShaderManager.cpp 
${SOURCE_DIR}/Renderer/Renderer.cpp 
${SOURCE_DIR}/Renderer/Mesh.cpp 
${SOURCE_DIR}/Renderer/TextureManager.cpp 
${SOURCE_DIR}/Renderer/MaterialManager.cpp 
${SOURCE_DIR}/Renderer/TextureFormats/Dtx.cpp 
${SOURCE_DIR}/Renderer/Texture.cpp 
${SOURCE_DIR}/Renderer/Material.cpp 
${SOURCE_DIR}/Renderer/ShadowUtils.cpp 
${SOURCE_DIR}/debug.cpp 
${SOURCE_DIR}/GameTime.cpp 
${SOURCE_DIR}/SoundManager.cpp
${SOURCE_DIR}/Scene.cpp 
${SOURCE_DIR}/Component.cpp 
${SOURCE_DIR}/GameObject.cpp 
${SOURCE_DIR}/Object.cpp 
${SOURCE_DIR}/IComponent.cpp 
)

add_library(${PROJECT_NAME} STATIC ${HEADER_FILES} ${SOURCE_FILES})

set_target_properties(${PROJECT_NAME} PROPERTIES
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib
    ARCHIVE_OUTPUT_DIRECTORY_DEBUG ${CMAKE_CURRENT_SOURCE_DIR}/lib/debug
    ARCHIVE_OUTPUT_DIRECTORY_RELEASE ${CMAKE_CURRENT_SOURCE_DIR}/lib/release
    ARCHIVE_OUTPUT_DIRECTORY_MINSIZEREL ${CMAKE_CURRENT_SOURCE_DIR}/lib/minsizerel
    ARCHIVE_OUTPUT_DIRECTORY_RELWITHDEBINFO ${CMAKE_CURRENT_SOURCE_DIR}/lib/relwithdebinfo
)

target_link_libraries(${PROJECT_NAME} PUBLIC glad OpenAL)
if (NOT EMSCRIPTEN)
    find_package(OpenGL REQUIRED)
    target_link_libraries(${PROJECT_NAME} PUBLIC glfw glm OpenGL::GL)
endif ()
target_include_directories(${PROJECT_NAME} PUBLIC ${SOURCE_DIR})

#include_directories(${SOURCE_DIR})

target_compile_definitions(${PROJECT_NAME} PUBLIC GLM_ENABLE_EXPERIMENTAL)

add_subdirectory(tools)
add_subdirectory(game)
add_subdirectory(runtime)
