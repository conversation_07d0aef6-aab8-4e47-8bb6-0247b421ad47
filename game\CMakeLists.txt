cmake_minimum_required(VERSION 3.25)

project(glenEdit VERSION 0.0.1)

set(CMAKE_CXX_STANDARD 17)

# Set static linking flags for Windows (optional)
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -static-libgcc -static-libstdc++ -static")

# ========= Paths =========
set(SRC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../src)
set(RES_DIR ${CMAKE_CURRENT_SOURCE_DIR}/resources)
#set(OUT_DIR ${CMAKE_CURRENT_SOURCE_DIR}/bin/${CMAKE_BUILD_TYPE})
set(OUT_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../example)

target_include_directories(${PROJECT_NAME} PRIVATE ${SRC_DIR})
target_link_libraries(${PROJECT_NAME} PRIVATE glen)

# ========= MyComponent DLL (Plugin) =========
add_library(glen-game SHARED
    dllmain.cpp
    MyComponent.cpp
    MyComponent2.cpp
)

target_include_directories(glen-game PRIVATE ${SRC_DIR})
target_link_libraries(glen-game PRIVATE glen)

# Set output directory for the DLL
set_target_properties(glen-game PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${OUT_DIR}
    LIBRARY_OUTPUT_DIRECTORY ${OUT_DIR}
    ARCHIVE_OUTPUT_DIRECTORY ${OUT_DIR}
    OUTPUT_NAME "glen-game" # Ensures DLL is named cleanly
    PREFIX "" # Removes the "lib" prefix
    #link gcc libraries statically
    LINK_FLAGS "-static-libgcc -static-libstdc++"
)

if (WIN32)
    set_target_properties(${PROJECT_NAME} PROPERTIES
        WIN32_EXECUTABLE $<IF:$<CONFIG:Release,MinSizeRel>,ON,OFF>
    )
endif ()

#if msvc /SUBSYSTEM:CONSOLE
if (MSVC)
    set_target_properties(${PROJECT_NAME} PROPERTIES
        LINK_FLAGS "/SUBSYSTEM:CONSOLE"
    )
endif ()