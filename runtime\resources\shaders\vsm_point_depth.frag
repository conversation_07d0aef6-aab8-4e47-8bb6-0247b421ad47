#version 450 core

layout(location = 0) out vec2 FragMoments;

uniform vec3 lightPos;
uniform float far_plane;
uniform sampler2D albedo;
uniform bool discardTransparent;

in VS_OUT {
    vec3 FragPos;
    vec2 TexCoords;
} fs_in;

void main()
{
    // Always sample the diffuse texture for alpha discard
    vec4 texColor = texture(albedo, fs_in.TexCoords);

    // Discard transparent fragments
    if (discardTransparent && texColor.a < 0.1) {
        discard;
    }

    // Calculate distance from light to fragment
    float lightDistance = length(fs_in.FragPos - lightPos);

    // Normalize distance by far plane
    float depth = lightDistance / far_plane;

    // Calculate moments for VSM
    float moment1 = depth;
    float moment2 = depth * depth;

    // Store moments in RG channels
    FragMoments = vec2(moment1, moment2);
}
