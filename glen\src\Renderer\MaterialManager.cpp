#include "MaterialManager.h"

std::unordered_map<std::string, std::shared_ptr<Material>> MaterialManager::materials;

void MaterialManager::LoadMaterial(const std::string &name, const std::shared_ptr<Material> &material)
{
    if (materials.find(name) != materials.end())
    {
        throw std::runtime_error("Material already exists: " + name);
    }
    materials[name] = material;
}

std::shared_ptr<Material> MaterialManager::GetMaterial(const std::string &name)
{
    auto it = materials.find(name);
    if (it == materials.end())
    {
        throw std::runtime_error("Material not found: " + name);
    }
    return it->second;
}

void MaterialManager::ClearMaterials()
{
    materials.clear();
}