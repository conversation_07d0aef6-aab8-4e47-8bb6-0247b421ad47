#version 450 core

layout(location = 0) in vec3 aPos;
layout(location = 1) in vec2 aTexCoord;

uniform mat4 uView;
uniform mat4 uProjection;
uniform vec3 uBillboardPos;
uniform float uBillboardSize;

out vec2 TexCoord;

void main()
{
    // Extract the right and up vectors from the view matrix
    vec3 cameraRight = vec3(uView[0][0], uView[1][0], uView[2][0]);
    vec3 cameraUp = vec3(uView[0][1], uView[1][1], uView[2][1]);
    
    // Calculate the world position of this vertex
    vec3 worldPos = uBillboardPos + 
                   cameraRight * aPos.x * uBillboardSize + 
                   cameraUp * aPos.y * uBillboardSize;
    
    // Transform to clip space
    gl_Position = uProjection * uView * vec4(worldPos, 1.0);
    
    // Pass through texture coordinates
    TexCoord = aTexCoord;
}
