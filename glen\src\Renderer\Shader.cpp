#include "Shader.h"
#include <debug.h>

Shader::Shader(const std::string &name)
{
    m_szName = name;
}
Shader::Shader()
{
    m_nID = 0;
    m_szName = "";
}

Shader::~Shader()
{
    glDeleteProgram(m_nID);
    m_nID = 0;
    m_szName = "";
}

void Shader::SetVec4(glm::vec4 value, const std::string &name)
{
    glUniform4fv(glGetUniformLocation(m_nID, name.c_str()), 1, glm::value_ptr(value));
}

void Shader::SetMat4(glm::mat4 value, const std::string &name)
{
    glUniformMatrix4fv(glGetUniformLocation(m_nID, name.c_str()), 1, GL_FALSE, glm::value_ptr(value));
}

void Shader::SetInt(int value, const std::string &name)
{
    glUniform1i(glGetUniformLocation(m_nID, name.c_str()), value);
}

void Shader::SetFloat(float value, const std::string &name)
{
    glUniform1f(glGetUniformLocation(m_nID, name.c_str()), value);
}

void Shader::SetVec3(glm::vec3 value, const std::string &name)
{
    glUniform3fv(glGetUniformLocation(m_nID, name.c_str()), 1, glm::value_ptr(value));
}

void Shader::SetVec2(glm::vec2 &value, const std::string &name)
{
    glUniform2fv(glGetUniformLocation(m_nID, name.c_str()), 1, glm::value_ptr(value));
}

void Shader::SetBool(bool value, const std::string &name)
{
    glUniform1i(glGetUniformLocation(m_nID, name.c_str()), (int)value);
}

void Shader::Bind()
{
    glUseProgram(m_nID);
}

void Shader::Unbind()
{
    glUseProgram(0);
}

void Shader::Load(const std::string &vertexPath, const std::string &fragmentPath, const std::string &geometryPath)
{
    // read the vertex, fragment, and (optionally) geometry shaders from file
    std::string vertexCode;
    std::string fragmentCode;
    std::string geometryCode;
    std::ifstream vShaderFile;
    std::ifstream fShaderFile;
    std::ifstream gShaderFile;

    try
    {
        vShaderFile.open(vertexPath);
        fShaderFile.open(fragmentPath);
        std::stringstream vShaderStream, fShaderStream, gShaderStream;

        vShaderStream << vShaderFile.rdbuf();
        fShaderStream << fShaderFile.rdbuf();

        vShaderFile.close();
        fShaderFile.close();

        vertexCode = vShaderStream.str();
        fragmentCode = fShaderStream.str();

        if (!geometryPath.empty())
        {
            gShaderFile.open(geometryPath);
            gShaderStream << gShaderFile.rdbuf();
            gShaderFile.close();
            geometryCode = gShaderStream.str();
        }
    }
    catch (std::ifstream::failure &e)
    {
        throw std::runtime_error("ERROR::SHADER::FILE_NOT_SUCCESFULLY_READ");
    }

    const char *vShaderCode = vertexCode.c_str();
    const char *fShaderCode = fragmentCode.c_str();
    const char *gShaderCode = geometryCode.empty() ? nullptr : geometryCode.c_str();

    GLuint vertex, fragment, geometry = 0;
    GLint success;
    GLchar infoLog[512];

    vertex = glCreateShader(GL_VERTEX_SHADER);
    glShaderSource(vertex, 1, &vShaderCode, nullptr);
    glCompileShader(vertex);

    glGetShaderiv(vertex, GL_COMPILE_STATUS, &success);
    if (!success)
    {
        glGetShaderInfoLog(vertex, 512, nullptr, infoLog);
        throw std::runtime_error("ERROR::SHADER::VERTEX::COMPILATION_FAILED\n" + std::string(infoLog));
    }

    fragment = glCreateShader(GL_FRAGMENT_SHADER);
    glShaderSource(fragment, 1, &fShaderCode, nullptr);
    glCompileShader(fragment);

    glGetShaderiv(fragment, GL_COMPILE_STATUS, &success);
    if (!success)
    {
        glGetShaderInfoLog(fragment, 512, nullptr, infoLog);
        throw std::runtime_error("ERROR::SHADER::FRAGMENT::COMPILATION_FAILED\n" + std::string(infoLog));
    }

    if (gShaderCode)
    {
        geometry = glCreateShader(GL_GEOMETRY_SHADER);
        glShaderSource(geometry, 1, &gShaderCode, nullptr);
        glCompileShader(geometry);

        glGetShaderiv(geometry, GL_COMPILE_STATUS, &success);
        if (!success)
        {
            glGetShaderInfoLog(geometry, 512, nullptr, infoLog);
            throw std::runtime_error("ERROR::SHADER::GEOMETRY::COMPILATION_FAILED\n" + std::string(infoLog));
        }
    }

    m_nID = glCreateProgram();
    glAttachShader(m_nID, vertex);
    if (gShaderCode)
        glAttachShader(m_nID, geometry);
    glAttachShader(m_nID, fragment);
    glLinkProgram(m_nID);

    glGetProgramiv(m_nID, GL_LINK_STATUS, &success);
    if (!success)
    {
        glGetProgramInfoLog(m_nID, 512, nullptr, infoLog);
        throw std::runtime_error("ERROR::SHADER::PROGRAM::LINKING_FAILED\n" + std::string(infoLog));
    }

    glDeleteShader(vertex);
    glDeleteShader(fragment);
    if (gShaderCode)
        glDeleteShader(geometry);
}

void Shader::Compile()
{
    glLinkProgram(m_nID);
    glValidateProgram(m_nID);
}

GLuint Shader::GetShaderID()
{
    return m_nID;
}

void Shader::SetName(const std::string &name)
{
    m_szName = name;
}

std::string Shader::GetName()
{
    return m_szName;
}

std::vector<ShaderUniform> Shader::GetActiveUniforms()
{
    std::vector<ShaderUniform> uniforms;
    GLint uniformCount;
    glGetProgramiv(m_nID, GL_ACTIVE_UNIFORMS, &uniformCount);

    for (GLint i = 0; i < uniformCount; ++i)
    {
        ShaderUniform uniform;
        char uniformName[256];
        GLsizei length;
        glGetActiveUniform(m_nID, i, sizeof(uniformName), &length, &uniform.uniformSize, &uniform.uniformType, uniformName);
        uniform.uniformName = std::string(uniformName, length);
        uniform.uniformID = glGetUniformLocation(m_nID, uniformName);
        uniforms.push_back(uniform);
    }

    return uniforms;
}

std::vector<ShaderAttribute> Shader::GetActiveAttributes()
{
    std::vector<ShaderAttribute> attributes;
    GLint numAttributes;
    glGetProgramiv(m_nID, GL_ACTIVE_ATTRIBUTES, &numAttributes);
    for (int i = 0; i < numAttributes; i++)
    {
        ShaderAttribute attribute;
        char attributeName[256];
        GLsizei length;
        glGetActiveAttrib(m_nID, i, sizeof(attributeName), &length, &attribute.attributeID, &attribute.attributeType, attributeName);
        attribute.attributeName = std::string(attributeName, length);
        attributes.push_back(attribute);
    }

    return attributes;
}