#pragma once

#include <glad/gl.h>
#include <glm/glm.hpp>
#include <Components/Light.h>

class Shadowmaps
{
public:
    enum class ShadowmapType
    {
        DIRECTIONAL,
        POINT,
        SPOT
    };

    Shadowmaps(uint8_t type, int width, int height)
        : type(static_cast<ShadowmapType>(type)), m_nWidth(width), n_nHeight(height)
    {
        CreateShadowMap();
    }
    ~Shadowmaps()
    {
        if (m_shadowMapTexture)
            glDeleteTextures(1, &m_shadowMapTexture);
        if (m_shadowMapFBO)
            glDeleteFramebuffers(1, &m_shadowMapFBO);
    }

    void CreateShadowMap()
    {
        switch (type)
        {
        case ShadowmapType::DIRECTIONAL:
            CreateDirectionalShadowMap();
            break;
        case ShadowmapType::SPOT:
            CreateSpotShadowMap();
            break;
        case ShadowmapType::POINT:
            CreatePointShadowMap();
            break;
        }
    }

    void CreateDirectionalShadowMap()
    {
        glGenFramebuffers(1, &m_shadowMapFBO);
        glGenTextures(1, &m_shadowMapTexture);
        glBindTexture(GL_TEXTURE_2D, m_shadowMapTexture);
        glTexImage2D(GL_TEXTURE_2D, 0, GL_DEPTH_COMPONENT, m_nWidth, n_nHeight, 0, GL_DEPTH_COMPONENT, GL_FLOAT, nullptr);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_NEAREST);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_NEAREST);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_BORDER);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_BORDER);
        float borderColor[] = {1.0, 1.0, 1.0, 1.0};
        glTexParameterfv(GL_TEXTURE_2D, GL_TEXTURE_BORDER_COLOR, borderColor);

        glBindFramebuffer(GL_FRAMEBUFFER, m_shadowMapFBO);
        glFramebufferTexture2D(GL_FRAMEBUFFER, GL_DEPTH_ATTACHMENT, GL_TEXTURE_2D, m_shadowMapTexture, 0);
        glDrawBuffer(GL_NONE);
        glReadBuffer(GL_NONE);
        glBindFramebuffer(GL_FRAMEBUFFER, 0);
    }

    void CreateSpotShadowMap()
    {
        // Spotlights use a 2D depth map, just like directional lights
        CreateDirectionalShadowMap();
    }

    void CreatePointShadowMap()
    {
        glGenFramebuffers(1, &m_shadowMapFBO);
        glGenTextures(1, &m_shadowMapTexture);
        glBindTexture(GL_TEXTURE_CUBE_MAP, m_shadowMapTexture);
        for (unsigned int i = 0; i < 6; ++i)
        {
            // Use RG32F for VSM (stores depth and depth²)
            glTexImage2D(GL_TEXTURE_CUBE_MAP_POSITIVE_X + i, 0, GL_RG32F, m_nWidth, n_nHeight, 0, GL_RG, GL_FLOAT, nullptr);
        }
        glTexParameteri(GL_TEXTURE_CUBE_MAP, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
        glTexParameteri(GL_TEXTURE_CUBE_MAP, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
        glTexParameteri(GL_TEXTURE_CUBE_MAP, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
        glTexParameteri(GL_TEXTURE_CUBE_MAP, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
        glTexParameteri(GL_TEXTURE_CUBE_MAP, GL_TEXTURE_WRAP_R, GL_CLAMP_TO_EDGE);

        glBindFramebuffer(GL_FRAMEBUFFER, m_shadowMapFBO);
        // VSM uses color attachment for RG32F data, not depth attachment
        glFramebufferTexture(GL_FRAMEBUFFER, GL_COLOR_ATTACHMENT0, m_shadowMapTexture, 0);

        // Still need a depth buffer for depth testing
        GLuint depthTexture;
        glGenTextures(1, &depthTexture);
        glBindTexture(GL_TEXTURE_CUBE_MAP, depthTexture);
        for (unsigned int i = 0; i < 6; ++i)
        {
            glTexImage2D(GL_TEXTURE_CUBE_MAP_POSITIVE_X + i, 0, GL_DEPTH_COMPONENT24, m_nWidth, n_nHeight, 0, GL_DEPTH_COMPONENT, GL_FLOAT, nullptr);
        }
        glTexParameteri(GL_TEXTURE_CUBE_MAP, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
        glTexParameteri(GL_TEXTURE_CUBE_MAP, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
        glTexParameteri(GL_TEXTURE_CUBE_MAP, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
        glTexParameteri(GL_TEXTURE_CUBE_MAP, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
        glTexParameteri(GL_TEXTURE_CUBE_MAP, GL_TEXTURE_WRAP_R, GL_CLAMP_TO_EDGE);
        glFramebufferTexture(GL_FRAMEBUFFER, GL_DEPTH_ATTACHMENT, depthTexture, 0);

        glBindFramebuffer(GL_FRAMEBUFFER, 0);
    }

    void bindShadowMap(int face)
    {
        glBindFramebuffer(GL_FRAMEBUFFER, m_shadowMapFBO);
        if (type == ShadowmapType::POINT)
        {
            // For VSM point lights, attach the correct face for color rendering
            glFramebufferTexture2D(GL_FRAMEBUFFER, GL_COLOR_ATTACHMENT0, GL_TEXTURE_CUBE_MAP_POSITIVE_X + face, m_shadowMapTexture, 0);
        }
        glViewport(0, 0, m_nWidth, n_nHeight);
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT); // Clear both color and depth for VSM
    }

    void unbindShadowMap()
    {
        glBindFramebuffer(GL_FRAMEBUFFER, 0);
    }

    GLuint getShadowMapTexture() const
    {
        if (m_shadowMapTexture == 0)
            return 0;
        return m_shadowMapTexture;
    }
    GLuint getShadowMapFBO() const
    {
        return m_shadowMapFBO;
    }

    int getWidth() const
    {
        return m_nWidth;
    }
    int getHeight() const
    {
        return n_nHeight;
    }

private:
    ShadowmapType type;
    int m_nWidth, n_nHeight;
    GLuint m_shadowMapFBO = 0;
    GLuint m_shadowMapTexture = 0;
};