#pragma once

#include "glm/vec3.hpp"
#include "glm/mat4x4.hpp"
#include "glm/glm.hpp"
#include "glm/gtx/quaternion.hpp"

#include <Component.h>
#include <Renderer/Shadowmaps.h>
#include <Renderer/FrameBuffer.h>
#include <debug.h>

// Forward declarations
class Camera;

class Light : public Component
{
public:
enum LightType
{
	DIRECTIONAL,
	POINT,
	SPOT,
};

public:

    LightType m_Type = LightType::POINT;
    float m_fIntensity = 1.0f;
    float m_fRange = 6.0f;
    glm::vec3 m_vColor = glm::vec3(1.0f, 1.0f, 1.0f);;
    bool m_bShadowEnabled = false;
    bool m_bSoftShadows = true; // Enable/disable Gaussian blur for soft shadows
    int m_iShadowBlurIterations = 8; // Number of blur passes for smoother shadows

	// Default constructor

	Light() { name = "Light"; }
    Light(const std::string inName)
    {
        name = inName;
    }
	Light(const Light&) = default;

	void SetColor(const glm::vec3& color)
	{
		m_vColor = color;
	}

	~Light() override
	{
		Debug::Log(ReturnTypes::SUCCESS, "Light::~Light() - Light component destroyed");
	}

	void Update() override;
	void Start() override;

	//get m_shadowMap
	// Returns a pointer to the shadow map, or nullptr if not set
	Shadowmaps* GetShadowMap() const
	{
		return m_shadowMap ? m_shadowMap.get() : nullptr;
	}


	std::vector<BuiltInFieldInfo> GetBuiltInFields() override
    {
		std::vector<BuiltInFieldInfo> fields;
		fields.reserve(7);
		fields.push_back(BuiltInFieldInfo{"int", "LightType", &m_Type});
		fields.push_back(BuiltInFieldInfo{"float", "Intensity", &m_fIntensity});
		fields.push_back(BuiltInFieldInfo{"float", "Range", &m_fRange});
		fields.push_back(BuiltInFieldInfo{"vec3", "Color", &m_vColor});
		fields.push_back(BuiltInFieldInfo{"bool", "ShadowEnabled", &m_bShadowEnabled});
		fields.push_back(BuiltInFieldInfo{"bool", "SoftShadows", &m_bSoftShadows});
		fields.push_back(BuiltInFieldInfo{"int", "BlurIterations", &m_iShadowBlurIterations});
		return fields;
    }

	// Directional light specific methods
	glm::mat4 CalculateDirectionalLightSpaceMatrix(class Camera* camera, float nearPlane, float farPlane) const;
	glm::vec3 GetDirectionalLightDirection() const;

	// Get the light space matrix for directional lights
	glm::mat4 GetLightSpaceMatrix() const { return m_mLightSpaceMatrix; }

	// Update the light space matrix (called during shadow pass)
	void UpdateLightSpaceMatrix(class Camera* camera);

	// Set shadow map resolution for better quality
	void SetShadowMapSize(float size) {
		m_fShadowMapSize = size;
		m_bNeedToInitializeShadowmap = true; // Force recreation
		m_bNeedToInitializePingPongBuffers = true; // Force ping pong buffer recreation
	}

	// Ping pong shadow map blur functionality (works for both directional and point lights)
	void BlurShadowMap(int blurIterations = -1); // -1 uses m_iShadowBlurIterations
	void InitializePingPongBuffers();

	// Get the final blurred shadow map texture
	GLuint GetBlurredShadowMapTexture() const;

	private:
	// Helper methods for blur functionality
	void BlurDirectionalShadowMap(int blurIterations);
	void BlurPointShadowMap(int blurIterations);

	public:

	// Get current shadow map size
	float GetShadowMapSize() const { return m_fShadowMapSize; }

	private:
	std::unique_ptr<Shadowmaps> m_shadowMap = nullptr;

	bool m_bNeedToInitializeShadowmap = true;

	float m_fShadowMapSize = 1024.0f; // Increased resolution for smoother shadows

	glm::mat4 m_mShadowMatrix = glm::mat4(1.0f);
	glm::mat4 m_mProjectionMatrix = glm::mat4(1.0f);
	glm::mat4 m_mViewMatrix = glm::mat4(1.0f);
	glm::mat4 m_mLightSpaceMatrix = glm::mat4(1.0f);

	// Ping pong buffers for shadow map blurring (both directional and point lights)
	std::unique_ptr<FrameBuffer> m_pBlurredShadowBuffer = nullptr;
	std::unique_ptr<FrameBuffer> m_pPingPongShadowBuffer = nullptr;
	bool m_bNeedToInitializePingPongBuffers = true;

	friend class Renderer; // Allow Renderer to access private members for shadow map creation and updates
};

