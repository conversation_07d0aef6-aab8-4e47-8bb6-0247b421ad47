#version 450 core

uniform vec3 lightPos;
uniform float far_plane;

uniform sampler2D albedo;
uniform bool discardTransparent; // Control whether to discard transparent fragments

in VS_OUT {
    vec3 FragPos;
    vec2 TexCoords;
} fs_in;

void main()
{
    vec4 texColor = texture(albedo, fs_in.TexCoords);

    // Discard the fragment if the alpha value is below a threshold
    if (discardTransparent && texColor.a < 0.1) {
        discard;
    }

    float lightDistance = length(fs_in.FragPos - lightPos);
    // Map to [0,1] for depth
    gl_FragDepth = lightDistance / far_plane;
}