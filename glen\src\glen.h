#pragma once

//include all the headers needed for the renderer
#include "Renderer/Renderer.h"
#include "Renderer/Shader.h"
#include "Renderer/TextureManager.h"
#include "Renderer/FrameBuffer.h"
#include "Renderer/Material.h"
#include "Renderer/ShaderManager.h"
#include "Renderer/TextureFormats/Dtx.h"
#include "Renderer/Mesh.h"
#include "Renderer/MaterialManager.h"

//ENGINE STUFF
#include "SoundManager.h"
#include "InputManager.h"
#include "GameObject.h"
#include "GameTime.h"
#include "Raycast3D.h"
#include "RaycastHit.h"
#include "Object.h"
#include "Scene.h"
#include "types.h"

//COMPONENTS
#include "Component.h"
#include "Components/Camera.h"
#include "Components/Light.h"
#include "Components/MeshRenderer.h"
#include "Components/Transform.h"
#include "Components/Sound.h"