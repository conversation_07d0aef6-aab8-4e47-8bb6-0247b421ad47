#include "Mesh.h"
#include "Renderer.h"
#include "Shader.h"

glm::vec3 AABB::min = glm::vec3(0.0f);
glm::vec3 AABB::max = glm::vec3(0.0f);

Mesh::SubMesh::SubMesh()
    : vertices(std::make_unique<std::vector<glm::vec3>>()),
      normals(std::make_unique<std::vector<glm::vec3>>()),
      tangents(std::make_unique<std::vector<glm::vec3>>()),
      texCoords(std::make_unique<std::vector<glm::vec2>>()),
      vertexColors(std::make_unique<std::vector<glm::vec4>>()),
      indices(std::make_unique<std::vector<GLuint>>()),
      texture(""), isAlphaTest(0), isTransparent(false),
      bounds(),
      VAO(0), VBO(0), EBO(0)
      {
        bounds.min = glm::vec3(FLT_MAX);
        bounds.max = glm::vec3(-FLT_MAX);
      }

Mesh::SubMesh::~SubMesh()
{
    glDeleteVertexArrays(1, &VAO);
    glDeleteBuffers(1, &VBO);
    glDeleteBuffers(1, &EBO);
}

void Mesh::SubMesh::SetupSubMesh()
{
    glGenVertexArrays(1, &VAO);
    glGenBuffers(1, &VBO);
    glGenBuffers(1, &EBO);

    glBindVertexArray(VAO);

    std::vector<float> packedData;
    for (size_t i = 0; i < vertices->size(); ++i)
    {
        packedData.push_back((*vertices)[i].x);
        packedData.push_back((*vertices)[i].y);
        packedData.push_back((*vertices)[i].z);

        packedData.push_back((*normals)[i].x);
        packedData.push_back((*normals)[i].y);
        packedData.push_back((*normals)[i].z);

        packedData.push_back((*tangents)[i].x);
        packedData.push_back((*tangents)[i].y);
        packedData.push_back((*tangents)[i].z);

        packedData.push_back((*texCoords)[i].x);
        packedData.push_back((*texCoords)[i].y);

        // packedData.push_back((*vertexColors)[i].x);
        // packedData.push_back((*vertexColors)[i].y);
        // packedData.push_back((*vertexColors)[i].z);
        // packedData.push_back((*vertexColors)[i].w);
        packedData.push_back(1.0f);
        packedData.push_back(1.0f);
        packedData.push_back(1.0f);
        packedData.push_back(1.0f);
    }

    glBindBuffer(GL_ARRAY_BUFFER, VBO);
    glBufferData(GL_ARRAY_BUFFER, packedData.size() * sizeof(float), packedData.data(), GL_STATIC_DRAW);

    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, EBO);
    glBufferData(GL_ELEMENT_ARRAY_BUFFER, indices->size() * sizeof(GLuint), indices->data(), GL_STATIC_DRAW);

    glEnableVertexAttribArray(0);
    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, 15 * sizeof(float), (void *)0);

    glEnableVertexAttribArray(1);
    glVertexAttribPointer(1, 3, GL_FLOAT, GL_FALSE, 15 * sizeof(float), (void *)(3 * sizeof(float)));

    glEnableVertexAttribArray(2);
    glVertexAttribPointer(2, 3, GL_FLOAT, GL_FALSE, 15 * sizeof(float), (void *)(6 * sizeof(float)));

    glEnableVertexAttribArray(3);
    glVertexAttribPointer(3, 2, GL_FLOAT, GL_FALSE, 15 * sizeof(float), (void *)(9 * sizeof(float)));

    glEnableVertexAttribArray(4);
    glVertexAttribPointer(4, 4, GL_FLOAT, GL_FALSE, 15 * sizeof(float), (void *)(11 * sizeof(float)));

    glBindVertexArray(0);

    // Calculate the AABB for the submesh
    for (const auto &vertex : *vertices)
    {
        bounds.min = glm::min(bounds.min, vertex);
        bounds.max = glm::max(bounds.max, vertex);
    }
}

Mesh::SubMesh::SubMesh(SubMesh &&other) noexcept
{
    vertices = std::move(other.vertices);
    normals = std::move(other.normals);
    tangents = std::move(other.tangents);
    texCoords = std::move(other.texCoords);
    vertexColors = std::move(other.vertexColors);
    indices = std::move(other.indices);
    texture = std::move(other.texture);
    isAlphaTest = other.isAlphaTest;
    isTransparent = other.isTransparent;
    VAO = other.VAO;
    VBO = other.VBO;
    EBO = other.EBO;
    bounds = other.bounds;

    other.VAO = 0;
    other.VBO = 0;
    other.EBO = 0;
}

Mesh::SubMesh& Mesh::SubMesh::operator=(SubMesh&& other) noexcept
{
    if (this != &other)
    {
        vertices = std::move(other.vertices);
        normals = std::move(other.normals);
        tangents = std::move(other.tangents);
        texCoords = std::move(other.texCoords);
        vertexColors = std::move(other.vertexColors);
        indices = std::move(other.indices);
        texture = std::move(other.texture);
        isAlphaTest = other.isAlphaTest;
        isTransparent = other.isTransparent;
        bounds = other.bounds;

        VAO = other.VAO;
        VBO = other.VBO;
        EBO = other.EBO;

        other.VAO = 0;
        other.VBO = 0;
        other.EBO = 0;
    }
    return *this;
}

Mesh::Mesh() {}

Mesh::Mesh(const std::string &path)
{
    std::ifstream file(path, std::ios::binary);
    if (!file)
    {
        throw std::runtime_error("Failed to open file: " + path);
    }

    uint32_t subMeshCount = 0;
    file.read(reinterpret_cast<char *>(&subMeshCount), sizeof(uint32_t));

    for (uint32_t i = 0; i < subMeshCount; ++i)
    {
        SubMesh subMesh;

        uint32_t materialNameLength = 0;
        file.read(reinterpret_cast<char *>(&materialNameLength), sizeof(uint32_t));
        subMesh.texture.resize(materialNameLength);
        file.read(&subMesh.texture[0], materialNameLength);

        //read single byte for transparency
        file.read(reinterpret_cast<char *>(&subMesh.isAlphaTest), sizeof(uint8_t));

        if(subMesh.isAlphaTest)
        {
           printf("Mesh is transparent\n");
        }

        uint32_t vertexCount = 0;
        file.read(reinterpret_cast<char *>(&vertexCount), sizeof(uint32_t));

        for (uint32_t j = 0; j < vertexCount; ++j)
        {
            glm::vec3 vertex;
            file.read(reinterpret_cast<char *>(&vertex), sizeof(glm::vec3));
            subMesh.vertices->push_back(vertex);

            glm::vec3 normal;
            file.read(reinterpret_cast<char *>(&normal), sizeof(glm::vec3));
            subMesh.normals->push_back(normal);

            glm::vec3 tangent;
            file.read(reinterpret_cast<char *>(&tangent), sizeof(glm::vec3));
            subMesh.tangents->push_back(tangent);

            glm::vec2 texCoord;
            file.read(reinterpret_cast<char *>(&texCoord), sizeof(glm::vec2));
            subMesh.texCoords->push_back(texCoord);

            glm::vec4 vertexColor;
            file.read(reinterpret_cast<char *>(&vertexColor), sizeof(glm::vec4));
            subMesh.vertexColors->push_back(vertexColor);
        }

        uint32_t indicesCount = 0;
        file.read(reinterpret_cast<char *>(&indicesCount), sizeof(uint32_t));

        std::vector<GLuint> indices(indicesCount);
        file.read(reinterpret_cast<char *>(indices.data()), indicesCount * sizeof(GLuint));
        subMesh.indices->assign(indices.begin(), indices.end());

        subMesh.SetupSubMesh();
        subMeshes.push_back(std::move(subMesh));
    }

    file.close();

    // Calculate the AABB for the entire mesh
    for (const auto &subMesh : subMeshes)
    {
        bounds.min = glm::min(bounds.min, subMesh.bounds.min);
        bounds.max = glm::max(bounds.max, subMesh.bounds.max);
    }
}

Mesh::~Mesh()
{
    ClearMesh();
}

void Mesh::SetupMesh()
{
    // No need to set up a global VAO, VBO, or EBO for the entire mesh
    // Each submesh has its own VAO, VBO, and EBO
}

std::vector<float> Mesh::GetPackedMeshData(const std::vector<glm::vec3> &vertices,
                                           const std::vector<glm::vec3> &normals,
                                           const std::vector<glm::vec3> &tangents,
                                           const std::vector<glm::vec2> &texCoords,
                                           const std::vector<glm::vec4> &vertexColors) const
{
    std::vector<float> packedData;
    size_t vertexCount = vertices.size();

    for (size_t i = 0; i < vertexCount; ++i)
    {
        packedData.push_back(vertices[i].x);
        packedData.push_back(vertices[i].y);
        packedData.push_back(vertices[i].z);

        if (i < normals.size())
        {
            packedData.push_back(normals[i].x);
            packedData.push_back(normals[i].y);
            packedData.push_back(normals[i].z);
        }
        else
        {
            packedData.push_back(0.0f);
            packedData.push_back(0.0f);
            packedData.push_back(0.0f);
        }

        if (i < tangents.size())
        {
            packedData.push_back(tangents[i].x);
            packedData.push_back(tangents[i].y);
            packedData.push_back(tangents[i].z);
        }
        else
        {
            packedData.push_back(0.0f);
            packedData.push_back(0.0f);
            packedData.push_back(0.0f);
        }

        if (i < texCoords.size())
        {
            packedData.push_back(texCoords[i].x);
            packedData.push_back(texCoords[i].y);
        }
        else
        {
            packedData.push_back(0.0f);
            packedData.push_back(0.0f);
        }

        if (i < vertexColors.size())
        {
            packedData.push_back(vertexColors[i].x);
            packedData.push_back(vertexColors[i].y);
            packedData.push_back(vertexColors[i].z);
            packedData.push_back(vertexColors[i].w);
        }
        else
        {
            packedData.push_back(1.0f);
            packedData.push_back(1.0f);
            packedData.push_back(1.0f);
            packedData.push_back(1.0f);
        }
    }

    return packedData;
}

void Mesh::DrawMesh(std::shared_ptr<Shader> shader, MeshRenderer &meshRenderer, bool isDepthOnly)
{
    std::vector<const SubMesh*> transparentSubMeshes;
    std::shared_ptr<Material> material = nullptr;

    glDepthMask(GL_TRUE);
    glDisable(GL_BLEND);

    int meshIndex = 0;
    for (auto &subMesh : subMeshes)
    {
        material = meshRenderer.GetMaterialAtIndex(meshIndex);

        // auto subMeshTexture = Renderer::GetTextureManager()->GetTexture(subMesh.texture);

        // // // Check if string contains water07.DTX
        // if (subMeshTexture && subMeshTexture->GetName().find("water07.DTX") != std::string::npos)
        // {
        //     subMesh.isTransparent = true;
        //     transparentSubMeshes.push_back(&subMesh);
        //     meshIndex++;
        //     continue;
        // }

        glBindVertexArray(subMesh.VAO);

        glActiveTexture(GL_TEXTURE0);
        // if (subMeshTexture)
        // {
            glBindTexture(GL_TEXTURE_2D, material->GetUniformValue<int>("albedo"));
        // }
        // else
        // {
        //     glBindTexture(GL_TEXTURE_2D, 0);
        // }

        //glBindTexture(GL_TEXTURE_2D, meshRenderer.GetMaterialAtIndex(meshIndex)->GetUniforms()["albedo"].textureID);

        glUniform1i(glGetUniformLocation(shader->GetShaderID(), "albedo"), 0);
        glUniform1i(glGetUniformLocation(shader->GetShaderID(), "discardTransparent"), subMesh.isAlphaTest ? 1 : 0);

        //set mainColor
        glm::vec4 mainColor = material->GetUniformValue<glm::vec4>("mainColor");
        material->GetShader()->SetVec4(mainColor, "mainColor");
        
        glDrawElements(GL_TRIANGLES, subMesh.indices->size(), GL_UNSIGNED_INT, 0);

        glBindTexture(GL_TEXTURE_2D, 0);
        meshIndex++;
    }

    meshIndex = 0;
    // Draw transparent submeshes
    if (!transparentSubMeshes.empty())
    {
        material = meshRenderer.GetMaterialAtIndex(meshIndex);

        glDepthMask(GL_FALSE);
        glEnable(GL_BLEND);
        glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);

        for (const auto *subMesh : transparentSubMeshes)
        {
            glBindVertexArray(subMesh->VAO);

            glActiveTexture(GL_TEXTURE0);
            auto textureObj = Renderer::GetTextureManager()->GetTexture(subMesh->texture);
            if (textureObj)
            {
                glBindTexture(GL_TEXTURE_2D, material->GetUniformValue<int>("albedo"));
                //glBindTexture(GL_TEXTURE_2D, textureObj->GetTextureID());
            }
            else
            {
                glBindTexture(GL_TEXTURE_2D, 0);
            }


            glUniform1i(glGetUniformLocation(shader->GetShaderID(), "albedo"), 0);
            glUniform1i(glGetUniformLocation(shader->GetShaderID(), "discardTransparent"), 0);

            material->GetShader()->SetVec4(material->GetUniformValue<glm::vec4>("mainColor"), "mainColor");

            glDrawElements(GL_TRIANGLES, subMesh->indices->size(), GL_UNSIGNED_INT, 0);

            glBindTexture(GL_TEXTURE_2D, 0);
            meshIndex++;
        }

        glDepthMask(GL_TRUE); // Re-enable depth writing
        glDisable(GL_BLEND);
    }

    glBindVertexArray(0);
}

void Mesh::ClearMesh()
{
    for (auto &subMesh : subMeshes)
    {
        glDeleteVertexArrays(1, &subMesh.VAO);
        glDeleteBuffers(1, &subMesh.VBO);
        glDeleteBuffers(1, &subMesh.EBO);
    }
    
}
