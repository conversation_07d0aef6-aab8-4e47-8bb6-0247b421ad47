#include "Dtx.h"
#include <vector>
#include <iterator>
#include <glad/gl.h>
#include <GLFW/glfw3.h>

#include <stdexcept>
#include <fstream>
#include <memory>

#define GL_COMPRESSED_RGB_S3TC_DXT1_EXT   0x83F0
#define GL_COMPRESSED_RGBA_S3TC_DXT1_EXT  0x83F1
#define GL_COMPRESSED_RGBA_S3TC_DXT3_EXT  0x83F2
#define GL_COMPRESSED_RGBA_S3TC_DXT5_EXT  0x83F3
#define GL_BGR                            0x80E0
#define GL_BGRA                           0x80E1

static unsigned int GetTextureFormat(uint8_t identElement)
{
    switch (identElement)
    {
        case 6: // DXT5
            return GL_COMPRESSED_RGBA_S3TC_DXT5_EXT;
        case 5: // DXT3
            return GL_COMPRESSED_RGBA_S3TC_DXT3_EXT;
        case 4: // DXT1
            return GL_COMPRESSED_RGBA_S3TC_DXT1_EXT;
        default: // BGRA
            return GL_BGRA;
    }
}

static std::unique_ptr<std::vector<unsigned char>> FlipImage(const DTX::DTXHeader &header, bool onX, bool onY)
{
    // Flip the image on the X and Y-axis
    std::unique_ptr<std::vector<unsigned char>> rgbaBuffer = std::make_unique<std::vector<unsigned char>>(header.baseWidth * header.baseHeight * 4);

    // Read the texture from the GPU and store it in the RGBA buffer, uncompressing it
    glGetTexImage(GL_TEXTURE_2D, 0, GL_RGBA, GL_UNSIGNED_BYTE, rgbaBuffer->data());

    int width = header.baseWidth;
    int height = header.baseHeight;
    int channels = 4; // Assuming 4 channels (RGBA)
    int rowSize = width * channels;

    if (onY)
    {
        for (int y = 0; y < height / 2; ++y)
        {
            int topRow = y * rowSize;
            int bottomRow = (height - 1 - y) * rowSize;
            for (int x = 0; x < rowSize; ++x)
            {
                std::swap((*rgbaBuffer)[topRow + x], (*rgbaBuffer)[bottomRow + x]);
            }
        }
    }

    if (onX)
    {
        for (int y = 0; y < height; ++y)
        {
            int rowStart = y * rowSize;
            for (int x = 0; x < width / 2; ++x)
            {
                int left = rowStart + x * channels;
                int right = rowStart + (width - 1 - x) * channels;
                for (int c = 0; c < channels; ++c)
                {
                    std::swap((*rgbaBuffer)[left + c], (*rgbaBuffer)[right + c]);
                }
            }
        }
    }

    return rgbaBuffer;
}

std::shared_ptr<Texture> DTX::LoadDTX(const std::string &dtxName)
{
    // Load the DTX file
    std::ifstream file(dtxName, std::ios::binary);
    if (!file.is_open())
    {
        printf("Failed to open file: %s\n", dtxName.c_str());
        throw std::runtime_error("ERROR::DTX::FILE_NOT_SUCCESFULLY_READ");
    }

    // Read the header
    DTXHeader header;
    file.read(reinterpret_cast<char *>(&header), sizeof(DTXHeader));

    // Jump to texture data, jump 128 bytes from the current position
    file.seekg(128, std::ios::cur);

    // Determine the texture format
    uint32_t textureFormat = GetTextureFormat(header.extraData[2]);

    // Calculate the buffer size based on the format
    size_t bufferSize;
    if (textureFormat == GL_COMPRESSED_RGB_S3TC_DXT1_EXT)
    {
        bufferSize = ((header.baseWidth + 3) / 4) * ((header.baseHeight + 3) / 4) * 8;
    }
    else if (textureFormat == GL_COMPRESSED_RGBA_S3TC_DXT3_EXT || textureFormat == GL_COMPRESSED_RGBA_S3TC_DXT5_EXT)
    {
        bufferSize = ((header.baseWidth + 3) / 4) * ((header.baseHeight + 3) / 4) * 16;
    }
    else if (textureFormat == GL_COMPRESSED_RGBA_S3TC_DXT1_EXT)
    {
        bufferSize = ((header.baseWidth + 3) / 4) * ((header.baseHeight + 3) / 4) * 8;
    }
    else if (textureFormat == GL_BGRA)
    {
        bufferSize = header.baseWidth * header.baseHeight * 4;
    }
    else
    {
        throw std::runtime_error("ERROR::DTX::UNSUPPORTED_TEXTURE_FORMAT");
    }

    // Read the texture data into a buffer
    std::vector<unsigned char> buffer(bufferSize);
    file.read(reinterpret_cast<char *>(buffer.data()), bufferSize);

    //verify the file was read
    if (!file)
    {
        printf("Failed to read file: %s\n", dtxName.c_str());
        throw std::runtime_error("ERROR::DTX::FILE_NOT_SUCCESFULLY_READ");
    }

    file.close();

    // Create a texture
    std::shared_ptr<Texture> texture = std::make_shared<Texture>();

    texture->SetName(dtxName);
    texture->SetWidth(header.baseWidth);
    texture->SetHeight(header.baseHeight);
    texture->SetChannels(4); // Assuming 4 channels for DXT3 and DXT5

    // Set the texture ID
    GLuint textureID;
    glGenTextures(1, &textureID);
    texture->SetTextureID(textureID);

    // Load the texture into OpenGL
    glBindTexture(GL_TEXTURE_2D, texture->GetTextureID());
    glCompressedTexImage2D(GL_TEXTURE_2D, 0, textureFormat, header.baseWidth, header.baseHeight, 0, bufferSize, buffer.data());

    // Set the texture wrapping and filtering options
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_REPEAT);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_REPEAT);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR_MIPMAP_LINEAR);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);

    auto rgbaBuffer = FlipImage(header, false, true);

    // Generate new mipmaps
    glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, header.baseWidth, header.baseHeight, 0, GL_RGBA, GL_UNSIGNED_BYTE, rgbaBuffer->data());
    glGenerateMipmap(GL_TEXTURE_2D);

    // Unbind the texture
    glBindTexture(GL_TEXTURE_2D, 0);

    // Return the texture
    return texture;
}