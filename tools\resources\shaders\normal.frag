#version 450 core

in VS_OUT {
    vec3 worldNormal;
    vec2 texCoords;
} fs_in;

out vec4 FragColor;

uniform sampler2D albedo;
uniform bool discardTransparent = true;

void main()
{
    // Check for transparency if needed
    if (discardTransparent) {
        vec4 texColor = texture(albedo, fs_in.texCoords);
        if (texColor.a < 0.1) {
            discard;
        }
    }
    
    // Normalize and encode world-space normal to [0,1] range
    vec3 normal = normalize(fs_in.worldNormal);
    vec3 encodedNormal = normal * 0.5 + 0.5;
    
    FragColor = vec4(encodedNormal, 1.0);
}