#include "Material.h"

Material::Material()
{
    m_szName = "DefaultMaterial";
    m_pShader = nullptr;
    m_Uniforms = std::unordered_map<std::string, ShaderUniform>();
    m_Attributes = std::unordered_map<std::string, ShaderAttribute>();
}

Material::~Material()
{
    m_Uniforms.clear();
    m_Attributes.clear();
    m_pShader = nullptr;
    m_szName = "";
}

void Material::SetShader(std::shared_ptr<Shader> shader)
{
    this->m_pShader = shader;

    SetupMaterialUniforms();
    SetupMaterialAttributes();
}


void Material::SetupMaterialUniforms()
{
    for (auto& uniform : m_pShader->GetActiveUniforms())
    {
        this->m_Uniforms[uniform.uniformName] = uniform;
    }
}

void Material::SetupMaterialAttributes()
{
    for (auto& attribute :  m_pShader->GetActiveAttributes())
    {
        this->m_Attributes[attribute.attributeName] = attribute;
    }
}

void Material::UseMaterial()
{
    m_pShader->Bind();

}