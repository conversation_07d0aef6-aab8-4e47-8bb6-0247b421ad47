#version 450 core

in vec3 nearPoint;
in vec3 farPoint;

out vec4 FragColor;

uniform vec3 cameraPos;
uniform float gridScale;
uniform vec3 gridColor;
uniform float gridAlpha;
uniform mat4 view;
uniform mat4 projection;
uniform vec2 screenSize;

float computeDepth(vec3 pos) {
    vec4 clip_space_pos = projection * view * vec4(pos.xyz, 1.0);
    return (clip_space_pos.z / clip_space_pos.w);
}

float computeLinearDepth(vec3 pos) {
    vec4 clip_space_pos = projection * view * vec4(pos.xyz, 1.0);
    float clip_space_depth = (clip_space_pos.z / clip_space_pos.w) * 2.0 - 1.0;
    float near = 0.1;
    float far = 1000.0;
    float linearDepth = (2.0 * near * far) / (far + near - clip_space_depth * (far - near));
    return linearDepth / far;
}

vec4 grid(vec3 fragPos3D, float scale, bool drawAxis) {
    vec2 coord = fragPos3D.xz * scale;
    vec2 derivative = fwidth(coord);
    vec2 grid = abs(fract(coord - 0.5) - 0.5) / derivative;
    float line = min(grid.x, grid.y);
    float minimumz = min(derivative.y, 1);
    float minimumx = min(derivative.x, 1);
    vec4 color = vec4(gridColor, gridAlpha - min(line, 1.0));
    
    // z axis (blue)
    if(fragPos3D.x > -0.1 * minimumx && fragPos3D.x < 0.1 * minimumx)
        color.z = 1.0;
    // x axis (red)
    if(fragPos3D.z > -0.1 * minimumz && fragPos3D.z < 0.1 * minimumz)
        color.x = 1.0;
    return color;
}

void main() {
    float t = -nearPoint.y / (farPoint.y - nearPoint.y);
    vec3 fragPos3D = nearPoint + t * (farPoint - nearPoint);
    
    // Skip if intersection point is behind camera
    if (t <= 0) {
        discard;
    }
    
    // Set proper depth
    float gridDepth = computeDepth(fragPos3D);
    gl_FragDepth = gridDepth * 0.5 + 0.5;
    
    // Calculate distance from camera for fading
    float distanceFromCamera = length(fragPos3D - cameraPos);
    float fadeDistance = 50.0;
    float fading = 1.0 - smoothstep(0.0, fadeDistance, distanceFromCamera);
    
    FragColor = (grid(fragPos3D, gridScale, true) + grid(fragPos3D, gridScale * 10, true));
    FragColor.a *= fading;
    
    // Only show grid lines - discard if alpha is too low
    if (FragColor.a < 0.02) {
        discard;
    }
}