#pragma once

#include <vector>
#include <memory>
#include "GameObject.h"

class Scene : public std::enable_shared_from_this<Scene>
{
public:
    Scene();
    ~Scene();

    void Update();
    void Start();
    void SetName(const std::string& name);
    std::string GetName() const { return m_szName; }
    void AddGameObject(std::shared_ptr<GameObject> gameObject);
    void RemoveGameObject(std::shared_ptr<GameObject> gameObject);
    std::vector<std::shared_ptr<GameObject>> GetGameObjects();
    std::shared_ptr<GameObject> GetGameObjectByName(const std::string& name);
    std::shared_ptr<GameObject> GetGameObjectByIndex(int index)
    {
        if (index >= 0 && index < static_cast<int>(m_pGameObjects.size()))
        {
            return m_pGameObjects[index];
        }
        return nullptr; // Return nullptr if index is out of bounds
    }

    static void SetActiveScene(std::shared_ptr<Scene> scene);
    static std::shared_ptr<Scene> GetActiveScene();

private:
    std::vector<std::shared_ptr<GameObject>> m_pGameObjects;
    std::string m_szName;

    static std::shared_ptr<Scene> s_pActiveScene; // Pointer to the active scene

};

void AddGameObject(std::shared_ptr<GameObject> gameObject);