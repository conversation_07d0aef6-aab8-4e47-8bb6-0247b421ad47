import bpy
import struct

obj = bpy.context.active_object

mesh = obj.data

vertices = []
normals = []
tangents = []
tex_coords = []
vertex_colors = []
indices = []

mesh.calc_tangents()

uv_layer = mesh.uv_layers.active.data
color_layer = mesh.vertex_colors.active.data

vertex_map = {}
index = 0

for poly in mesh.polygons:
    for loop_index in poly.loop_indices:
        loop = mesh.loops[loop_index]
        vert = mesh.vertices[loop.vertex_index]

        vertex = vert.co
        normal = vert.normal
        tangent = loop.tangent
        uv = uv_layer[loop_index].uv
        color = color_layer[loop_index].color

        key = (vertex.x, vertex.y, vertex.z, normal.x, normal.y, normal.z, tangent.x, tangent.y, tangent.z, uv.x, uv.y, 1.0, 1.0, 1.0, 1.0)

        if key not in vertex_map:
            vertex_map[key] = index
            vertices.append((vertex.x, vertex.y, vertex.z))
            normals.append((normal.x, normal.y, normal.z))
            tangents.append((tangent.x, tangent.y, tangent.z))
            tex_coords.append((uv.x, uv.y))
            vertex_colors.append((color.r, color.g, color.b, color.a))
            index += 1

        indices.append(vertex_map[key])

with open(filepath, 'wb') as f:
    # Write number of vertices
    f.write(struct.pack('I', len(vertices)))

    # Write vertices
    for v in vertices:
        f.write(struct.pack('3f', *v))

    # Write normals
    for n in normals:
        f.write(struct.pack('3f', *n))

    # Write tangents
    for t in tangents:
        f.write(struct.pack('3f', *t))

    # Write texture coordinates
    for uv in tex_coords:
        f.write(struct.pack('2f', *uv))

    # Write vertex colors
    for c in vertex_colors:
        f.write(struct.pack('4f', *c))

    # Write number of indices
    f.write(struct.pack('I', len(indices)))

    # Write indices
    for i in indices:
        f.write(struct.pack('I', i))

print(f"Mesh exported to {filepath}")