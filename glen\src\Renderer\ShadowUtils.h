#pragma once

#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <vector>
#include "Components/Camera.h"

class ShadowUtils
{
public:
    // Structure to hold frustum corners
    struct FrustumCorners
    {
        std::vector<glm::vec3> corners; // 8 corners of the frustum
        glm::vec3 center;               // Center of the frustum
    };

    // Calculate the 8 corners of a camera's view frustum in world space
    static FrustumCorners CalculateCameraFrustumCorners(Camera* camera, float nearPlane, float farPlane);
    
    // Calculate optimal light view and projection matrices for directional shadows
    // based on camera frustum
    static glm::mat4 CalculateFittedLightViewMatrix(const glm::vec3& lightDirection, const FrustumCorners& frustum);
    
    // Calculate optimal orthographic projection matrix for directional shadows
    static glm::mat4 CalculateFittedLightProjectionMatrix(const glm::mat4& lightView, const FrustumCorners& frustum);
    
    // Combined function to get the complete light space matrix
    static glm::mat4 CalculateFittedLightSpaceMatrix(Camera* camera, const glm::vec3& lightDirection,
                                                     float nearPlane, float farPlane);

    // Calculate bounds of visible geometry within camera frustum
    static glm::vec3 CalculateVisibleGeometryBounds(Camera* camera, float nearPlane, float farPlane);

private:
    // Helper function to transform frustum corners to light space
    static std::vector<glm::vec3> TransformToLightSpace(const std::vector<glm::vec3>& worldCorners, 
                                                        const glm::mat4& lightView);
    
    // Helper function to calculate bounding box in light space
    static void CalculateLightSpaceBounds(const std::vector<glm::vec3>& lightSpaceCorners,
                                          glm::vec3& minBounds, glm::vec3& maxBounds);
};
