#pragma once

#include <memory>
#include <Renderer/TextureManager.h>

class DTX
{
public:

    enum class DTXFlags
    {
        FULLBRIGHT = 0x01,
        PREFER16BIT = 0x02,
        UNK1 = 0x04,
        UNK2 = 0x08,
        UNK3 = 0x10,
        UNK4 = 0x20,
        UNK5 = 0x40,
        PREFER4444 = 0x80,
        PREFER5551 = 0x100,
    };

   struct textureSize
    {
        int width;
        int height;
        int engineWidth;
        int engineHeight;
    };

    struct DTXHeader
    {
        uint32_t resType;
        int32_t version;
        uint16_t baseWidth, baseHeight;
        uint16_t mipMapCount;
        uint16_t nSections;
        int32_t IFlags;
        int32_t userFlags;

        uint8_t extraData[12];
    };

    static std::shared_ptr<Texture> LoadDTX(const std::string &dtxName);
};