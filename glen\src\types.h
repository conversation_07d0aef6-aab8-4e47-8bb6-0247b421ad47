#pragma once

#include <cstdint>
#include <string>
#include <cmath>
#include <algorithm>
#include <glm/glm.hpp>

enum class ShaderType : uint8_t {
    Vertex,
    Fragment,
    Geometry
};

enum class ReturnTypes : uint8_t 
{
    GENERALERROR = 0,
    SUCCESS = 1,
    SHADER_EXISTS = 2,
    SHADER_NOT_FOUND = 3,
    SHADER_COMPILED = 4
};

class AABB
{
public:
    static glm::vec3 min;
    static glm::vec3 max;

    static void Initialize()
    {
        min = glm::vec3(0.0f);
        max = glm::vec3(0.0f);
    }

    static void SetBounds(const glm::vec3& newMin, const glm::vec3& newMax)
    {
        min = newMin;
        max = newMax;
    }

    static bool Intersects(const AABB &other)
    {
        return (min.x <= other.max.x && max.x >= other.min.x) &&
               (min.y <= other.max.y && max.y >= other.min.y) &&
               (min.z <= other.max.z && max.z >= other.min.z);
    }

    static bool Contains(const glm::vec3 &point)
    {
        return (point.x >= min.x && point.x <= max.x) &&
               (point.y >= min.y && point.y <= max.y) &&
               (point.z >= min.z && point.z <= max.z);
    }

    static void Expand(const glm::vec3 &point)
    {
        min.x = std::min(min.x, point.x);
        min.y = std::min(min.y, point.y);
        min.z = std::min(min.z, point.z);

        max.x = std::max(max.x, point.x);
        max.y = std::max(max.y, point.y);
        max.z = std::max(max.z, point.z);
    }

    static std::string ToString()
    {
        return "Min: (" + std::to_string(min.x) + ", " + std::to_string(min.y) + ", " + std::to_string(min.z) + ")\n" +
               "Max: (" + std::to_string(max.x) + ", " + std::to_string(max.y) + ", " + std::to_string(max.z) + ")";
    }

    //equals
    static bool Equals(const AABB &other)
    {
        return min == other.min && max == other.max;
    }

    //not equals
    static bool NotEquals(const AABB &other)
    {
        return !Equals(other);
    }

    //less than
    static bool LessThan(const AABB &other)
    {
        return min.x < other.min.x && min.y < other.min.y && min.z < other.min.z &&
               max.x < other.max.x && max.y < other.max.y && max.z < other.max.z;
    }

    //greater than
    static bool GreaterThan(const AABB &other)
    {
        return min.x > other.min.x && min.y > other.min.y && min.z > other.min.z &&
               max.x > other.max.x && max.y > other.max.y && max.z > other.max.z;
    }

    //less than or equal to
    static bool LessThanOrEqual(const AABB &other)
    {
        return min.x <= other.min.x && min.y <= other.min.y && min.z <= other.min.z &&
               max.x <= other.max.x && max.y <= other.max.y && max.z <= other.max.z;
    }

    //greater than or equal to
    static bool GreaterThanOrEqual(const AABB &other)
    {
        return min.x >= other.min.x && min.y >= other.min.y && min.z >= other.min.z &&
               max.x >= other.max.x && max.y >= other.max.y && max.z >= other.max.z;
    }

    //addition
    static AABB Add(const AABB &other)
    {
        AABB result;
        result.min = min + other.min;
        result.max = max + other.max;
        return result;
    }

    //subtraction
    static AABB Subtract(const AABB &other)
    {
        AABB result;
        result.min = min - other.min;
        result.max = max - other.max;
        return result;
    }

    //multiplication
    static AABB Multiply(const AABB &other)
    {
        AABB result;
        result.min = min * other.min;
        result.max = max * other.max;
        return result;
    }

    //division
    static AABB Divide(const AABB &other)
    {
        AABB result;
        result.min = min / other.min;
        result.max = max / other.max;
        return result;
    }

    static glm::vec3 GetCenter()
    {
        return (min + max) / 2.0f;
    }

    static glm::vec3 GetSize()
    {
        return max - min;
    }

    static float GetVolume()
    {
        glm::vec3 size = GetSize();
        return size.x * size.y * size.z;
    }
};