#pragma once

#include <string>
#include <fstream>
#include <sstream>
#include <iostream>
#include <stdexcept>
#include <unordered_map>
#include <memory>
#include "Shader.h"
#include <glm/gtc/type_ptr.hpp>
#include <memory>
#include <types.h>

class Shader;

class ShaderManager
{
public:
    ShaderManager();
    ~ShaderManager();
    ReturnTypes LoadShader(const std::string &shaderName, bool isDepth = false);
    ReturnTypes DeleteShader(const std::string &shaderName);
    std::shared_ptr<Shader> GetShader(const std::string &shaderName);

private:
    std::unordered_map<std::string, std::shared_ptr<Shader>> m_pShaders;
};