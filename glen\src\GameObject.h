#pragma once

#include "IComponent.h"
#include "Object.h"

#include <vector>
#include <memory>
#include <algorithm>

class Component;
class Transform;

class GameObject : public std::enable_shared_from_this<GameObject>, public Object
{
public:
    GameObject();
    GameObject(const std::string name);
    virtual ~GameObject(); // Added virtual destructor
    virtual void Update();
    
    virtual void Start();


    void SetName(const std::string& name) { this->name = name; }
    std::string GetName() const { return name; }

    template<class T>
    std::shared_ptr<T> AddComponent(std::shared_ptr<T> component);
    std::shared_ptr<Component> AddComponent(std::shared_ptr<Component> component);
    
    std::shared_ptr<IComponent> AddComponent(std::shared_ptr<IComponent> component);

    std::shared_ptr<GameObject> AddChild(std::shared_ptr<GameObject> child);
    void RemoveChild(std::shared_ptr<GameObject> child);
    void SetParent(std::shared_ptr<GameObject> gameObject) { this->parent = gameObject; }
    std::shared_ptr<GameObject> GetParent() const { return parent; }

    void PrintAllChildren() const
    {
        for (const auto& child : children)
        {
            printf("%s\n", child->name.c_str());
            child->PrintAllChildren();
        }
    }

    template<class T>
    std::shared_ptr<T> GetComponent() const;

    template<class T>
    std::shared_ptr<T> GetComponentByName(const std::string& name) const;

    template<class T>
    void GetComponents(std::vector<std::shared_ptr<T>>& componentContainer) const;

    template<class T>
    void RemoveComponent(std::shared_ptr<T> component);

    template<class T>
    void RemoveComponent();

    template<class T>
    void RemoveComponentByName(std::shared_ptr<T> component, const std::string& name);
    
    std::vector<std::shared_ptr<GameObject>> GetChildren() const { return children; }
    std::vector<std::shared_ptr<Component>> GetComponents() const { return components; }

    std::shared_ptr<GameObject> GetChildByName(const std::string& name) const
    {
        for (const auto& child : children)
        {
            if (child->name == name)
            {
                return child;
            }
        }
        return nullptr;
    }

    

    std::shared_ptr<Transform> GetTransform() const { return transform; }


private:


    std::shared_ptr<Transform> transform;
    std::vector<std::shared_ptr<Component>> components;
    std::vector<std::shared_ptr<IComponent>> icomponents;
    std::vector<std::shared_ptr<GameObject>> children;
    std::shared_ptr<GameObject> parent;
};

template<class T>
std::shared_ptr<T> GameObject::AddComponent(std::shared_ptr<T> component)
{
    static_assert(std::is_base_of<Component, T>::value, "T must inherit from Component");

    return std::static_pointer_cast<T>(AddComponent(std::static_pointer_cast<Component>(component)));
}

template<class T>
std::shared_ptr<T> GameObject::GetComponent() const
{
    static_assert(std::is_base_of<Component, T>::value, "T must inherit from Component");

    std::shared_ptr<T> result = nullptr;
    for (auto const& c : components)
    {
        result = std::dynamic_pointer_cast<T>(c);
        if (result != nullptr)
            break;
    }

    return result;
}

template<class T>
std::shared_ptr<T> GameObject::GetComponentByName(const std::string& name) const
{
    static_assert(std::is_base_of<Component, T>::value, "T must inherit from Component");

    for (auto const& c : components)
    {
        std::shared_ptr<T> component = std::dynamic_pointer_cast<T>(c);
        if (component != nullptr && component->name == name)
        {
            return component;
        }
    }
    return nullptr;
}

template<class T>
void GameObject::GetComponents(std::vector<std::shared_ptr<T>>& componentContainer) const
{
    static_assert(std::is_base_of<Component, T>::value, "T must inherit from Component");

    for (auto const& c : components)
    {
        std::shared_ptr<T> component = std::dynamic_pointer_cast<T>(c);
        if (component != nullptr)
        {
            componentContainer.push_back(component);
        }
    }
}

template<class T>
void GameObject::RemoveComponent(std::shared_ptr<T> component)
{
    static_assert(std::is_base_of<Component, T>::value, "T must inherit from Component");

    auto it = std::remove_if(components.begin(), components.end(),
        [&component](const std::shared_ptr<Component>& c) { return c == component; });
    components.erase(it, components.end());
}

template<class T>
void GameObject::RemoveComponent()
    {
        static_assert(std::is_base_of<Component, T>::value, "T must inherit from Component");

        auto it = std::remove_if(components.begin(), components.end(),
            [](const std::shared_ptr<Component>& c) { return std::dynamic_pointer_cast<T>(c) != nullptr; });
        components.erase(it, components.end());
    }
