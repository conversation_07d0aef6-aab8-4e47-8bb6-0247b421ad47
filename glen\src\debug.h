#pragma once

#include <types.h>

class Debug
{
public:
    static const uint32_t BLACK = 0;
    static const uint32_t BLUE = 1;
    static const uint32_t GREEN = 2;
    static const uint32_t CYAN = 3;
    static const uint32_t RED = 4;
    static const uint32_t MAGENTA = 5;
    static const uint32_t BROWN = 6;
    static const uint32_t LIGHTGRAY = 7;
    static const uint32_t DARKGRAY = 8;
    static const uint32_t LIGHTBLUE = 9;
    static const uint32_t LIGHTGREEN = 10;
    static const uint32_t LIGHTCYAN = 11;
    static const uint32_t LIGHTRED = 12;
    static const uint32_t LIGHTMAGENTA = 13;
    static const uint32_t YELLOW = 14;
    static const uint32_t WHITE = 15;

    static void Log(std::string message, const uint32_t consoleColor = WHITE);

    static void Log(const ReturnTypes &error, std::string message);
};