#version 450 core
layout (triangles) in;
layout (triangle_strip, max_vertices = 18) out;

in VS_OUT {
    vec3 FragPos;
    vec2 TexCoords;
} gs_in[];

out VS_OUT {
    vec3 FragPos;
    vec2 TexCoords;
} vs_out;

uniform mat4 shadowMatrices[6];

void main()
{
    for (int face = 0; face < 6; ++face)
    {
        for (int i = 0; i < 3; ++i)
        {
            gl_Position = shadowMatrices[face] * vec4(gs_in[i].FragPos, 1.0);
            vs_out.FragPos = gs_in[i].FragPos;
            vs_out.TexCoords = gs_in[i].TexCoords;
            gl_Layer = face;
            EmitVertex();
        }
        EndPrimitive();
    }
}