#pragma once

#include <Renderer/Shader.h>
#include <unordered_map>
#include <memory>
#include <types.h>

class Material
{
public:
    Material();
    ~Material();

    void SetShader(std::shared_ptr<Shader> shader);
    std::shared_ptr<Shader> GetShader() const { return m_pShader; }

    std::string GetName() const { return m_szName; }
    void SetName(const std::string &name) { this->m_szName = name; }

    void SetupMaterialUniforms();
    void SetupMaterialAttributes();

    std::unordered_map<std::string, ShaderUniform>& GetUniforms() { return m_Uniforms; }
    std::unordered_map<std::string, ShaderAttribute>& GetAttributes() { return m_Attributes; }

    void SetupUniformsToDefaultValues()
    {
        for (auto& uniform : m_Uniforms)
        {
            if (uniform.second.uniformType == GL_FLOAT)
                uniform.second.uniformValue.floatValue = 0.0f;
            else if (uniform.second.uniformType == GL_INT)
                uniform.second.uniformValue.intValue = 0;
            else if (uniform.second.uniformType == GL_BOOL)
                uniform.second.uniformValue.boolValue = false;
            else if (uniform.second.uniformType == GL_FLOAT_VEC2)
                uniform.second.uniformValue.vec2Value = glm::vec2(0.0f);
            else if (uniform.second.uniformType == GL_FLOAT_VEC3)
                uniform.second.uniformValue.vec3Value = glm::vec3(0.0f);
            else if (uniform.second.uniformType == GL_FLOAT_VEC4)
                uniform.second.uniformValue.vec4Value = glm::vec4(0.0f);
            else if (uniform.second.uniformType == GL_FLOAT_MAT4)
                uniform.second.uniformValue.mat4Value = glm::mat4(1.0f);
        }
    }

    //set uniform value
    template<typename T>
    void SetUniformValue(const std::string& name, const T& value)
    {
        auto it = m_Uniforms.find(name);
        if (it != m_Uniforms.end())
        {
            if constexpr (std::is_same_v<T, int>)
                it->second.uniformValue.intValue = value;
            else if constexpr (std::is_same_v<T, float>)
                it->second.uniformValue.floatValue = value;
            else if constexpr (std::is_same_v<T, bool>)
                it->second.uniformValue.boolValue = value;
            else if constexpr (std::is_same_v<T, glm::vec2>)
                it->second.uniformValue.vec2Value = value;
            else if constexpr (std::is_same_v<T, glm::vec3>)
                it->second.uniformValue.vec3Value = value;
            else if constexpr (std::is_same_v<T, glm::vec4>)
                it->second.uniformValue.vec4Value = value;
            else if constexpr (std::is_same_v<T, glm::mat4>)
                it->second.uniformValue.mat4Value = value;
        }
    }

    template<typename T>
    T GetUniformValue(const std::string& name) const
    {
        auto it = m_Uniforms.find(name);
        if (it != m_Uniforms.end())
        {
            if constexpr (std::is_same_v<T, int>)
                return it->second.uniformValue.intValue;
            else if constexpr (std::is_same_v<T, float>)
                return it->second.uniformValue.floatValue;
            else if constexpr (std::is_same_v<T, bool>)
                return it->second.uniformValue.boolValue;
            else if constexpr (std::is_same_v<T, glm::vec2>)
                return it->second.uniformValue.vec2Value;
            else if constexpr (std::is_same_v<T, glm::vec3>)
                return it->second.uniformValue.vec3Value;
            else if constexpr (std::is_same_v<T, glm::vec4>)
                return it->second.uniformValue.vec4Value;
            else if constexpr (std::is_same_v<T, glm::mat4>)
                return it->second.uniformValue.mat4Value;
        }
        if constexpr (std::is_same_v<T, int>)
            return 0;
        else if constexpr (std::is_same_v<T, float>)
            return 0.0f;
        else if constexpr (std::is_same_v<T, bool>)
            return false;
        else if constexpr (std::is_same_v<T, glm::vec2>)
            return glm::vec2(0.0f);
        else if constexpr (std::is_same_v<T, glm::vec3>)
            return glm::vec3(0.0f);
        else if constexpr (std::is_same_v<T, glm::vec4>)
            return glm::vec4(0.0f);
        else if constexpr (std::is_same_v<T, glm::mat4>)
            return glm::mat4(1.0f);
    }

    void UseMaterial();

private:
    std::shared_ptr<Shader> m_pShader;
    std::unordered_map<std::string, ShaderUniform> m_Uniforms;
    std::unordered_map<std::string, ShaderAttribute> m_Attributes;
    std::string m_szName;

};