#version 450 core

layout (location = 0) in vec3 position;
layout (location = 1) in vec3 normal;
layout (location = 2) in vec3 tangent;
layout (location = 3) in vec2 texcoord;
layout (location = 4) in vec4 color;

out VS_OUT {
    vec3 FragPos;
    vec2 TexCoords;
} vs_out;

uniform mat4 model;

void main()
{
    vec4 worldPosition = model * vec4(position, 1.0);
    vs_out.FragPos = worldPosition.xyz;
    vs_out.TexCoords = texcoord;
    gl_Position = worldPosition; // Not used, but required
}