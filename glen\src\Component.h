#pragma once

#include "Object.h"
#include "GameObject.h"
#include <memory>

struct BuiltInFieldInfo {
	std::string type;
    std::string name;
    void* ptr;
};

class Component : public Object
{
public:
	bool enabled = true;
public:
	Component() = default;
	virtual ~Component() { }

	virtual void Start() {};
	virtual void Update() {};

	inline GameObject* GetGameObject() const { return go; }

	Component& operator=(const Component& other);
	GameObject* go = nullptr;

	virtual std::vector<BuiltInFieldInfo> GetBuiltInFields() { return std::vector<BuiltInFieldInfo>(); };

private:
	void SetGameObject(GameObject& gameObject);

	friend GameObject;
};

