#pragma once

#include <string>
#include <fstream>
#include <sstream>
#include <iostream>
#include <stdexcept>
#include <unordered_map>
#include <memory>
#include <unordered_map>
#include "Texture.h"

class TextureManager
{
public:
    TextureManager();
    ~TextureManager();
    void LoadTexture(const std::string &textureName);
    void DeleteTexture(const std::string &textureName);
    std::shared_ptr<Texture> GetTexture(const std::string &textureName);
    void AddTexture(const std::string &textureName, std::shared_ptr<Texture> texture)
    {
        m_pTextures[textureName] = texture;
    }
    void CopyAllTexturesToProjectFolder();

private:
    std::unordered_map<std::string, std::shared_ptr<Texture>> m_pTextures;

};