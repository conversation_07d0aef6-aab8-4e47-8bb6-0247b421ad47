#include "Transform.h"
#include "debug.h"

void Transform::Update()
{
}

void Transform::Start()
{
}

std::vector<BuiltInFieldInfo> Transform::GetBuiltInFields()
{
    std::vector<BuiltInFieldInfo> fields;
    fields.reserve(3);
    fields.push_back(BuiltInFieldInfo{"vec3", "Position", &m_vPosition});
    fields.push_back(BuiltInFieldInfo{"vec3", "Rotation", &m_vRotation});
    fields.push_back(BuiltInFieldInfo{"vec3", "Scale", &m_vScale});
    return fields;
}