// MyComponent.h
#pragma once
#include <IComponent.h> // Shared interface with the editor
#include <Component.h>
#include "debug.h"

class MyComponent : public IComponent, public Component {
public:
    MyComponent()
    {
        name = "MyComponent";
    }
    ~MyComponent() override = default;

    void Start() override;
    void Update() override;
    const char* GetTypeName() const override;
    void SetGameObject(GameObject* obj) override;
    std::string GetField(const std::string& name) const override
    {
        return std::string();
    };
    bool SetField(const std::string& name, const std::string& value) override
    {
        return false;
    };
    std::vector<BuiltInFieldInfo> GetBuiltInFields() override;

    //PrintMembers
    void PrintMembers() override {};

    bool test = true;
    

private:
    
};
