#include "EditorGizmos.h"
#include "Editor.h"
#include <Components/Light.h>
#include <Components/MeshRenderer.h>
#include <imgui.h>
#include <iostream>

namespace Editor {

void EditorGizmos::RenderGizmosForPicking(Camera* camera)
{
    auto& editorState = EditorState::GetInstance();
    auto selectedObject = editorState.GetActiveGameObject();
    if (!selectedObject) return;
    
    // Update picking framebuffer size to match scene view
    if (s_pickingFrameBuffer->GetWidth() != static_cast<int>(s_sceneViewWidth) ||
        s_pickingFrameBuffer->GetHeight() != static_cast<int>(s_sceneViewHeight))
    {
        s_pickingFrameBuffer = std::make_unique<FrameBuffer>(
            static_cast<int>(s_sceneViewWidth), 
            static_cast<int>(s_sceneViewHeight), 
            FrameBuffer::FrameBufferType::COLOR
        );
    }
    
    // Save current OpenGL state
    GLint currentFramebuffer;
    glGetIntegerv(GL_FRAMEBUFFER_BINDING, &currentFramebuffer);

    GLfloat currentClearColor[4];
    glGetFloatv(GL_COLOR_CLEAR_VALUE, currentClearColor);

    // Bind picking framebuffer
    s_pickingFrameBuffer->Bind();

    // Clear to black (no selection)
    glClearColor(0.0f, 0.0f, 0.0f, 1.0f);
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
    
    // Calculate view and projection matrices
    glm::vec3 cameraPos = camera->GetGameObject()->GetTransform()->GetPosition();
    glm::vec3 cameraFront = camera->GetGameObject()->GetTransform()->GetForwardVector();
    glm::vec3 cameraUp = camera->GetGameObject()->GetTransform()->GetUpVector();
    glm::mat4 view = glm::lookAt(cameraPos, cameraPos + cameraFront, cameraUp);
    
    // Use the same aspect ratio calculation as the main rendering to ensure consistency
    float aspectRatio = s_sceneViewWidth / s_sceneViewHeight;
    glm::mat4 proj = glm::perspective(glm::radians(camera->fov), aspectRatio, camera->nearClip, camera->farClip);
    
    auto gizmoShader = Renderer::GetShaderManager()->GetShader("gizmos");
    if (!gizmoShader) 
    {
        s_pickingFrameBuffer->Unbind();
        return;
    }
    
    gizmoShader->Bind();
    
    // Disable backface culling and depth testing for picking
    glDisable(GL_CULL_FACE);
    glDisable(GL_DEPTH_TEST);
    
    auto transform = selectedObject->GetTransform();
    glm::vec3 pos = transform->GetWorldPosition();
    
    // Render X axis arrow in RED (255, 0, 0)
    glm::mat4 model = glm::translate(glm::mat4(1.0f), pos)
          * glm::rotate(glm::mat4(1.0f), glm::radians(-90.0f), glm::vec3(0, 0, 1)); // +Y to +X
    gizmoShader->SetMat4(proj * view * model, "uMVP");
    gizmoShader->SetVec3(glm::vec3(1.0f, 0.0f, 0.0f), "mainColor"); // Pure red
    gizmoShader->SetBool(false, "isSolid");
    glBindVertexArray(s_wireArrow.vao);
    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, s_wireArrow.eboTris);
    glDrawElements(GL_TRIANGLES, s_wireArrow.triIndexCount, GL_UNSIGNED_INT, 0);
    
    // Render Y axis arrow in GREEN (0, 255, 0)
    model = glm::translate(glm::mat4(1.0f), pos);
    gizmoShader->SetMat4(proj * view * model, "uMVP");
    gizmoShader->SetVec3(glm::vec3(0.0f, 1.0f, 0.0f), "mainColor"); // Pure green
    gizmoShader->SetBool(false, "isSolid");
    glBindVertexArray(s_wireArrow.vao);
    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, s_wireArrow.eboTris);
    glDrawElements(GL_TRIANGLES, s_wireArrow.triIndexCount, GL_UNSIGNED_INT, 0);
    
    // Render Z axis arrow in BLUE (0, 0, 255) - pointing towards -Z for OpenGL convention
    model = glm::translate(glm::mat4(1.0f), pos)
          * glm::rotate(glm::mat4(1.0f), glm::radians(-90.0f), glm::vec3(1, 0, 0)); // +Y to -Z
    gizmoShader->SetMat4(proj * view * model, "uMVP");
    gizmoShader->SetVec3(glm::vec3(0.0f, 0.0f, 1.0f), "mainColor"); // Pure blue
    gizmoShader->SetBool(false, "isSolid");
    glBindVertexArray(s_wireArrow.vao);
    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, s_wireArrow.eboTris);
    glDrawElements(GL_TRIANGLES, s_wireArrow.triIndexCount, GL_UNSIGNED_INT, 0);

    // Disable depth testing for plane handles to match visual rendering
    glDisable(GL_DEPTH_TEST);

    // Render plane handles with distinct colors for picking at the gizmo origin
    // XY plane in CYAN (0, 255, 255) - positioned at origin
    model = glm::translate(glm::mat4(1.0f), pos);
    gizmoShader->SetMat4(proj * view * model, "uMVP");
    gizmoShader->SetVec3(glm::vec3(0.0f, 1.0f, 1.0f), "mainColor"); // Pure cyan
    gizmoShader->SetBool(false, "isSolid");
    glBindVertexArray(s_plane.vao);
    glDrawElements(GL_TRIANGLES, s_plane.indexCount, GL_UNSIGNED_INT, 0);

    // XZ plane in MAGENTA (255, 0, 255) - positioned at origin
    model = glm::translate(glm::mat4(1.0f), pos)
          * glm::rotate(glm::mat4(1.0f), glm::radians(-90.0f), glm::vec3(1, 0, 0)); // Rotate to XZ plane
    gizmoShader->SetMat4(proj * view * model, "uMVP");
    gizmoShader->SetVec3(glm::vec3(1.0f, 0.0f, 1.0f), "mainColor"); // Pure magenta
    gizmoShader->SetBool(false, "isSolid");
    glBindVertexArray(s_plane.vao);
    glDrawElements(GL_TRIANGLES, s_plane.indexCount, GL_UNSIGNED_INT, 0);

    // YZ plane in YELLOW (255, 255, 0) - positioned at origin
    model = glm::translate(glm::mat4(1.0f), pos)
          * glm::rotate(glm::mat4(1.0f), glm::radians(90.0f), glm::vec3(0, 1, 0)); // Rotate to YZ plane
    gizmoShader->SetMat4(proj * view * model, "uMVP");
    gizmoShader->SetVec3(glm::vec3(1.0f, 1.0f, 0.0f), "mainColor"); // Pure yellow
    gizmoShader->SetBool(false, "isSolid");
    glBindVertexArray(s_plane.vao);
    glDrawElements(GL_TRIANGLES, s_plane.indexCount, GL_UNSIGNED_INT, 0);

    // Re-enable depth testing
    glEnable(GL_DEPTH_TEST);

    glBindVertexArray(0);
    
    // Re-enable depth testing and backface culling
    glEnable(GL_DEPTH_TEST);
    glEnable(GL_CULL_FACE);
    
    gizmoShader->Unbind();
    s_pickingFrameBuffer->Unbind();

    // Restore OpenGL state
    glBindFramebuffer(GL_FRAMEBUFFER, currentFramebuffer);
    glClearColor(currentClearColor[0], currentClearColor[1], currentClearColor[2], currentClearColor[3]);
}

EditorGizmos::GizmoAxis EditorGizmos::GetAxisFromPickingColor(unsigned char r, unsigned char g, unsigned char b, bool debug)
{
    // Pure red = X axis
    if (r > 200 && g < 50 && b < 50)
    {
        return GizmoAxis::X;
    }

    // Pure green = Y axis
    if (r < 50 && g > 200 && b < 50)
    {
        return GizmoAxis::Y;
    }

    // Pure blue = Z axis
    if (r < 50 && g < 50 && b > 200)
    {
        return GizmoAxis::Z;
    }

    // Pure cyan = XY plane (0, 255, 255)
    if (r < 50 && g > 200 && b > 200)
    {
        return GizmoAxis::XY;
    }

    // Pure magenta = XZ plane (255, 0, 255)
    if (r > 200 && g < 50 && b > 200)
    {
        return GizmoAxis::XZ;
    }

    // Pure yellow = YZ plane (255, 255, 0)
    if (r > 200 && g > 200 && b < 50)
    {
        return GizmoAxis::YZ;
    }

    return GizmoAxis::NONE;
}

} // namespace Editor
