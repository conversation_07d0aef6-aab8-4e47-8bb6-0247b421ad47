#include "UI_Inspector.h"
#include "../Editor/Editor.h"
#include <glen.h>
#include <Components/Light.h>
#include <imgui.h>
#include <imgui_internal.h>
#include <imgui_impl_glfw.h>
#include <imgui_impl_opengl3.h>
#include <vector>
#include <algorithm>

void RenderInspectorWindow()
{
    ImGui::Begin("Inspector");
    auto& editorState = Editor::EditorState::GetInstance();
    auto go = editorState.GetActiveGameObject();
    if(!go)
    {
        ImGui::Text("No GameObject selected.");
        ImGui::End();
        return;
    }
    ImGui::Text("GameObject: %s", go->GetName().c_str());
    ImGui::Separator();

    for (const auto& comp : go->GetComponents())
    {
        ImGui::BulletText("%s", comp->name.c_str());

        // Special handling for Light component to group shadow settings
        if (comp->name == "Light")
        {
            auto lightComp = std::dynamic_pointer_cast<Light>(comp);
            if (lightComp)
            {
                // Add shadow settings group
                if (lightComp->m_bShadowEnabled)
                {
                    ImGui::Indent();
                    ImGui::Text("Shadow Settings:");

                    // Soft/Sharp shadows toggle
                    bool softShadows = lightComp->m_bSoftShadows;
                    if (ImGui::Checkbox("Soft Shadows", &softShadows))
                    {
                        lightComp->m_bSoftShadows = softShadows;
                    }
                    ImGui::SameLine();
                    ImGui::TextDisabled("(?)");
                    if (ImGui::IsItemHovered())
                    {
                        ImGui::SetTooltip("Enable Gaussian blur for smooth shadow edges.\nDisable for sharp, pixelated shadows.");
                    }

                    // Blur iterations (only show if soft shadows are enabled)
                    if (lightComp->m_bSoftShadows)
                    {
                        int blurIterations = lightComp->m_iShadowBlurIterations;
                        if (ImGui::SliderInt("Blur Quality", &blurIterations, 1, 16))
                        {
                            lightComp->m_iShadowBlurIterations = blurIterations;
                        }
                        ImGui::SameLine();
                        ImGui::TextDisabled("(?)");
                        if (ImGui::IsItemHovered())
                        {
                            ImGui::SetTooltip("Higher values = smoother shadows but slower performance.\nDirectional: 1 pass per iteration\nPoint: 6 passes per iteration (one per cubemap face)");
                        }
                    }

                    // Shadow Map Preview
                    ImGui::Text("Shadow Map Preview:");
                    GLuint shadowTexture = lightComp->GetBlurredShadowMapTexture();
                    if (shadowTexture != 0)
                    {
                        float previewSize = 128.0f; // Size of the preview image

                        if (lightComp->m_Type == Light::LightType::DIRECTIONAL)
                        {
                            // For directional lights, show the 2D shadow map
                            ImGui::Text("Directional Shadow Map:");
                            ImGui::Image(reinterpret_cast<void*>(shadowTexture), ImVec2(previewSize, previewSize));
                            if (ImGui::IsItemHovered())
                            {
                                ImGui::SetTooltip("Grayscale visualization of directional light shadow map.\nShows the depth buffer from the light's perspective.\nDarker = closer to light, Lighter = farther from light");
                            }
                        }
                        else if (lightComp->m_Type == Light::LightType::POINT)
                        {
        
                           
                        }
                    }
                    else
                    {
                        ImGui::Text("No shadow map available");
                    }

                    ImGui::Unindent();
                    ImGui::Separator();
                }
            }
        }

        for (const auto& field : comp->GetBuiltInFields())
        {
            // Skip shadow-related fields for Light components (handled specially above)
            if (comp->name == "Light" && (field.name == "SoftShadows" || field.name == "BlurIterations"))
            {
                continue;
            }

            // Skip Range field for directional lights (Range doesn't apply to directional lights)
            if (comp->name == "Light" && field.name == "Range")
            {
                auto lightComp = std::dynamic_pointer_cast<Light>(comp);
                if (lightComp && lightComp->m_Type == Light::LightType::DIRECTIONAL)
                {
                    continue;
                }
            }

            std::string fieldName = comp->name + "." + field.name;
            if(field.type == "vec3")
            {
                if (field.name == "Rotation")
                {
                    glm::vec3 rotation = *(static_cast<glm::vec3*>(field.ptr));
                    rotation = glm::degrees(rotation);
                    if (ImGui::DragFloat3(fieldName.c_str(), &rotation[0], 0.1f))
                    {
                        *(static_cast<glm::vec3*>(field.ptr)) = glm::radians(rotation);
                    }
                }
                else if (field.name.find("Color") != std::string::npos)
                {
                    glm::vec3 color = *(static_cast<glm::vec3*>(field.ptr));
                    if (ImGui::ColorEdit3(fieldName.c_str(), &color[0]))
                    {
                        *(static_cast<glm::vec3*>(field.ptr)) = color;
                    }
                }
                else
                {
                    glm::vec3 value = *(static_cast<glm::vec3*>(field.ptr));
                    if (ImGui::DragFloat3(fieldName.c_str(), &value[0], 0.1f))
                    {
                        *(static_cast<glm::vec3*>(field.ptr)) = value;
                    }
                }
            }
            else if(field.type == "vec2")
            {
                glm::vec2 value = *(static_cast<glm::vec2*>(field.ptr));
                if (ImGui::DragFloat2(fieldName.c_str(), &value[0], 0.1f))
                {
                    *(static_cast<glm::vec2*>(field.ptr)) = value;
                }
            }
            else if(field.type == "float")
            {
                float value = *(static_cast<float*>(field.ptr));
                if (ImGui::DragFloat(fieldName.c_str(), &value, 0.1f))
                {
                    *(static_cast<float*>(field.ptr)) = value;
                }
            }
            else if(field.type == "int")
            {
                int value = *(static_cast<int*>(field.ptr));
                if (ImGui::InputInt(fieldName.c_str(), &value))
                {
                    *(static_cast<int*>(field.ptr)) = value;
                }
            }
            else if(field.type == "bool")
            {
                bool value = *(static_cast<bool*>(field.ptr));
                if (ImGui::Checkbox(fieldName.c_str(), &value))
                {
                    *(static_cast<bool*>(field.ptr)) = value;
                }
            }
            else if(field.type == "string")
            {
                std::string value = *(static_cast<std::string*>(field.ptr));
                char buffer[256];
                strncpy(buffer, value.c_str(), sizeof(buffer));
                buffer[sizeof(buffer) - 1] = '\0';
                if (ImGui::InputText(fieldName.c_str(), buffer, sizeof(buffer)))
                {
                    *(static_cast<std::string*>(field.ptr)) = std::string(buffer);
                }
            }
            else if(field.type == "Mesh")
            {
                Mesh* mesh = *(static_cast<Mesh**>(field.ptr));
                if (mesh)
                {
                    ImGui::Text("Mesh instance");
                    if (ImGui::Button(("Select Mesh##" + fieldName).c_str()))
                    {
                        // Open mesh selector
                        std::cout << "Selecting mesh for " << go->GetName() << std::endl;
                    }
                }
                else
                {
                    ImGui::Text("No mesh assigned.");
                }
            }
            else if(field.type == "Material")
            {
                Material* material = *(static_cast<Material**>(field.ptr));
                if (material)
                {
                    ImGui::Text("Material: %s", material->GetName().c_str());
                    if (ImGui::Button(("Select Material##" + fieldName).c_str()))
                    {
                        // Open material selector
                        std::cout << "Selecting material for " << go->GetName() << std::endl;
                    }
                }
                else
                {
                    ImGui::Text("No material assigned.");
                }
            }
            else if (field.type == "Camera")
            {
                Camera* camera = *(static_cast<Camera**>(field.ptr));
                if (camera)
                {
                    ImGui::Text("Camera: %s", camera->name.c_str());
                    if (ImGui::Button(("Select Camera##" + fieldName).c_str()))
                    {
                        // Open camera selector
                        std::cout << "Selecting camera for " << go->name << std::endl;
                    }
                }
                else
                {
                    ImGui::Text("No camera assigned.");
                }
            }
            
        }
        ImGui::Separator();
    }
    ImGui::End();
}

void RenderInspectorContextMenu()
{
    if (ImGui::BeginPopupContextItem("InspectorContextMenu"))
    {
        if (ImGui::MenuItem("Add Component"))
        {
            // Logic to add a component
            // This could open a dialog or show a list of components to add
        }
        if (ImGui::MenuItem("Remove Component"))
        {
            // Logic to remove the selected component
            auto go = Editor::EditorState::GetInstance().GetActiveGameObject();
            if (go)
            {
                //go->RemoveComponent(go->GetSelectedComponent());
            }
        }
        ImGui::EndPopup();
    }
}
