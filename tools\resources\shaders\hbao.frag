#version 450 core

in vec2 TexCoords;
out float FragColor;

uniform sampler2D gDepth;
uniform sampler2D gNormal;
uniform mat4 projection;
uniform mat4 view;
uniform vec2 screenSize;

// HBAO+ parameters
uniform float radius = 1.0;
uniform float bias = 0.025;
uniform float intensity = 1.0;
uniform int numDirections = 8;
uniform int numSteps = 4;

const float PI = 3.14159265359;
const float TAU = 6.28318530718;

// Random function for noise
float rand(vec2 co) {
    return fract(sin(dot(co.xy, vec2(12.9898, 78.233))) * 43758.5453);
}

// Convert depth to view space position
vec3 getViewPos(vec2 texCoord, float depth) {
    vec4 clipSpacePos = vec4(texCoord * 2.0 - 1.0, depth * 2.0 - 1.0, 1.0);
    vec4 viewSpacePos = inverse(projection) * clipSpacePos;
    return viewSpacePos.xyz / viewSpacePos.w;
}

// Get view space normal from world space normal
vec3 getViewNormal(vec2 texCoord) {
    vec3 worldNormal = texture(gNormal, texCoord).xyz * 2.0 - 1.0;
    return normalize((view * vec4(worldNormal, 0.0)).xyz);
}

float computeHBAO(vec2 texCoord, vec3 viewPos, vec3 viewNormal) {
    float occlusion = 0.0;
    float pixelRadius = radius / viewPos.z;
    
    // Random rotation angle
    float randomAngle = rand(texCoord) * TAU;
    
    for (int i = 0; i < numDirections; i++) {
        float angle = (float(i) / float(numDirections)) * TAU + randomAngle;
        vec2 direction = vec2(cos(angle), sin(angle));
        
        float maxHorizonAngle = -1.0;
        
        for (int j = 1; j <= numSteps; j++) {
            float stepSize = float(j) / float(numSteps);
            vec2 sampleTexCoord = texCoord + direction * pixelRadius * stepSize;
            
            // Check bounds
            if (sampleTexCoord.x < 0.0 || sampleTexCoord.x > 1.0 || 
                sampleTexCoord.y < 0.0 || sampleTexCoord.y > 1.0) {
                break;
            }
            
            float sampleDepth = texture(gDepth, sampleTexCoord).r;
            vec3 sampleViewPos = getViewPos(sampleTexCoord, sampleDepth);
            
            vec3 horizonVector = sampleViewPos - viewPos;
            float horizonLength = length(horizonVector);
            
            // Skip if sample is too far
            if (horizonLength > radius) continue;
            
            // Calculate horizon angle
            float horizonAngle = asin(clamp(horizonVector.z / horizonLength, -1.0, 1.0));
            
            // Apply distance falloff
            float falloff = 1.0 - smoothstep(0.0, radius, horizonLength);
            horizonAngle *= falloff;
            
            maxHorizonAngle = max(maxHorizonAngle, horizonAngle);
        }
        
        // Calculate occlusion for this direction
        float normalAngle = asin(clamp(dot(normalize(direction), viewNormal.xy), -1.0, 1.0));
        float occlusionAngle = max(maxHorizonAngle - normalAngle + bias, 0.0);
        occlusion += sin(occlusionAngle);
    }
    
    occlusion /= float(numDirections);
    return clamp(1.0 - occlusion * intensity, 0.0, 1.0);
}

void main() {
    float depth = texture(gDepth, TexCoords).r;
    
    // Skip background pixels
    if (depth >= 1.0) {
        FragColor = 1.0;
        return;
    }
    
    vec3 viewPos = getViewPos(TexCoords, depth);
    vec3 viewNormal = getViewNormal(TexCoords);
    
    float ao = computeHBAO(TexCoords, viewPos, viewNormal);
    FragColor = ao;
}