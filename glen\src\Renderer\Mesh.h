#pragma once

#include <vector>
#include <memory>
#include <glm/glm.hpp>
#include <glad/gl.h>
#include <fstream>
#include <sstream>
#include <stdexcept>
#include <string>
#include <types.h>

class MeshRenderer;
class Shader;

class Mesh
{
public:
    struct SubMesh
    {
        std::unique_ptr<std::vector<glm::vec3>> vertices;
        std::unique_ptr<std::vector<glm::vec3>> normals;
        std::unique_ptr<std::vector<glm::vec3>> tangents;
        std::unique_ptr<std::vector<glm::vec2>> texCoords;
        std::unique_ptr<std::vector<glm::vec4>> vertexColors;
        std::unique_ptr<std::vector<GLuint>> indices;
        std::string texture;
        bool isAlphaTest = false;
        bool isTransparent = false;
        AABB bounds;

        GLuint VAO, VBO, EBO;

        SubMesh();
        ~SubMesh();
        void SetupSubMesh();

        // Move constructor
        SubMesh(SubMesh&& other) noexcept;

        // Move assignment operator
        SubMesh& operator=(SubMesh&& other) noexcept;
    };

    std::vector<SubMesh> subMeshes;
    AABB bounds;

    Mesh();
    Mesh(const std::string &path);
    ~Mesh();

    void SetupMesh();
    std::vector<float> GetPackedMeshData(const std::vector<glm::vec3> &vertices,
                                         const std::vector<glm::vec3> &normals,
                                         const std::vector<glm::vec3> &tangents,
                                         const std::vector<glm::vec2> &texCoords,
                                         const std::vector<glm::vec4> &vertexColors) const;
    void DrawMesh(std::shared_ptr<Shader> shader, MeshRenderer &meshRenderer, bool isDepthOnly);
    void ClearMesh();

    Mesh(const Mesh &) = delete;
    Mesh &operator=(const Mesh &) = delete;

    Mesh(Mesh &&other) noexcept;
    Mesh &operator=(Mesh &&other) noexcept;
};