#version 450 core

layout(location = 0) out float FragDepth;

uniform sampler2D albedo;
uniform bool discardTransparent = true;
uniform float near_plane;
uniform float far_plane;

in vec2 TexCoords;

// Convert non-linear depth to linear depth
float linearizeDepth(float depth) {
    float z = depth * 2.0 - 1.0; // Convert to NDC
    return (2.0 * near_plane * far_plane) / (far_plane + near_plane - z * (far_plane - near_plane));
}

void main()
{
    // Alpha testing for transparent objects
    if (discardTransparent) {
        vec4 texColor = texture(albedo, TexCoords);
        if (texColor.a < 0.1) {
            discard;
        }
    }
    
    // Output linearized depth normalized to [0,1]
    float linearDepth = linearizeDepth(gl_FragCoord.z);
    FragDepth = linearDepth / far_plane;
}