bl_info = {
    "name": "Material and Texture Manager",
    "blender": (2, 80, 0),
    "category": "Object",
    "version": (1, 0, 0),
    "author": "Your Name",
    "description": "Strip material names and recursively search for textures."
}

import bpy
import os
import re
import bmesh
import struct

class MaterialTextureToolPanel(bpy.types.Panel):
    """Creates a panel in the 3D View"""
    bl_label = "Material and Texture Manager"
    bl_idname = "OBJECT_PT_material_texture_tool"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'Material Tools'

    def draw(self, context):
        layout = self.layout

        # Material Name Cleanup
        layout.label(text="Material Name Cleanup:")
        layout.operator("material.cleanup_names", text="Clean Material Names")

        # Texture Search
        layout.label(text="Texture Finder:")
        layout.prop(context.scene, "search_folder")
        layout.operator("material.search_textures", text="Search for Textures")

        # Update Material Textures
        layout.label(text="Update Material Textures:")
        layout.operator("material.update_textures", text="Update Textures to .tga")

        # Export Meshes
        layout.label(text="Export Meshes:")
        layout.prop(context.scene, "export_filepath")
        layout.operator("material.export_meshes", text="Export Meshes")

class CleanMaterialNamesOperator(bpy.types.Operator):
    """Cleans up material names by removing parentheses, 'Instance', and '_Chromakey' and anything past it"""
    bl_idname = "material.cleanup_names"
    bl_label = "Clean Material Names"

    def execute(self, context):
        materials = bpy.data.materials
        for mat in materials:
            if mat:
                original_name = mat.name
                cleaned_name = re.sub(r'\(.*?\)|Instance|_Chromakey.*', '', original_name).strip()
                mat.name = cleaned_name
                self.report({'INFO'}, f"Renamed '{original_name}' to '{cleaned_name}'")
        return {'FINISHED'}

class SearchTexturesOperator(bpy.types.Operator):
    """Searches recursively in a folder for textures based on material names and renames materials"""
    bl_idname = "material.search_textures"
    bl_label = "Search for Textures"

    def execute(self, context):
        search_folder = context.scene.search_folder
        if not os.path.isdir(search_folder):
            self.report({'ERROR'}, "Invalid folder path")
            return {'CANCELLED'}

        materials = bpy.data.materials
        for mat in materials:
            if mat:
                mat_name = mat.name
                texture_found = False

                for root, dirs, files in os.walk(search_folder):
                    for file in files:
                        if file.lower().startswith(mat_name.lower()):
                            relative_path = os.path.relpath(os.path.join(root, file), search_folder)
                            mat.name = relative_path
                            self.report({'INFO'}, f"Material '{mat_name}' renamed to '{relative_path}'")
                            texture_found = True
                            break

                    if texture_found:
                        break

                if not texture_found:
                    self.report({'WARNING'}, f"No matching texture found for material '{mat_name}'")

        return {'FINISHED'}

class UpdateTexturesOperator(bpy.types.Operator):
    """Replaces .dtx extension with .tga, sets up materials with the updated texture, and configures backface culling"""
    bl_idname = "material.update_textures"
    bl_label = "Update Textures to .tga"

    def execute(self, context):
        search_folder = context.scene.search_folder
        if not os.path.isdir(search_folder):
            self.report({'ERROR'}, "Invalid folder path")
            return {'CANCELLED'}

        materials = bpy.data.materials
        for mat in materials:
            if mat:
                mat_name = mat.name
                texture_path = os.path.join(search_folder, mat_name)
                if texture_path.endswith(".dtx"):
                    new_texture_path = texture_path.replace(".dtx", ".tga")
                    if os.path.exists(new_texture_path):
                        if not mat.use_nodes:
                            mat.use_nodes = True
                        nodes = mat.node_tree.nodes
                        links = mat.node_tree.links

                        # Ensure the material has a Principled BSDF node
                        principled_node = None
                        for node in nodes:
                            if isinstance(node, bpy.types.ShaderNodeBsdfPrincipled):
                                principled_node = node
                                break
                        if not principled_node:
                            principled_node = nodes.new(type="ShaderNodeBsdfPrincipled")
                            principled_node.location = (0, 0)

                        # Ensure the material has a Material Output node
                        output_node = None
                        for node in nodes:
                            if isinstance(node, bpy.types.ShaderNodeOutputMaterial):
                                output_node = node
                                break
                        if not output_node:
                            output_node = nodes.new(type="ShaderNodeOutputMaterial")
                            output_node.location = (200, 0)

                        # Link Principled BSDF to Material Output
                        if not any(link.to_node == output_node and link.from_node == principled_node for link in links):
                            links.new(principled_node.outputs[0], output_node.inputs[0])

                        # Add or update an Image Texture node
                        texture_node = None
                        for node in nodes:
                            if isinstance(node, bpy.types.ShaderNodeTexImage):
                                texture_node = node
                                break
                        if not texture_node:
                            texture_node = nodes.new(type="ShaderNodeTexImage")
                            texture_node.location = (-200, 0)

                        # Load the .tga texture
                        texture_node.image = bpy.data.images.load(new_texture_path)

                        # Link Image Texture to Base Color of Principled BSDF
                        if not any(link.to_node == principled_node and link.from_node == texture_node for link in links):
                            links.new(texture_node.outputs[0], principled_node.inputs[0])

                        # Enable backface culling
                        mat.use_backface_culling = True

                        self.report({'INFO'}, f"Updated material '{mat_name}' with texture '{new_texture_path}'")
                    else:
                        self.report({'WARNING'}, f"Texture '{new_texture_path}' does not exist")

        return {'FINISHED'}

class ExportMeshesOperator(bpy.types.Operator):
    """Exports selected meshes to a binary file"""
    bl_idname = "material.export_meshes"
    bl_label = "Export Meshes"

    def execute(self, context):
        export_filepath = context.scene.export_filepath
        if not export_filepath:
            self.report({'ERROR'}, "Invalid file path")
            return {'CANCELLED'}

        export_meshes(export_filepath)
        self.report({'INFO'}, f"Meshes exported to {export_filepath}")
        return {'FINISHED'}

def export_meshes(filepath):
    selected_objects = [obj for obj in bpy.context.selected_objects if obj.type == 'MESH']
    if not selected_objects:
        print("No mesh objects selected.")
        return

    mesh_data = []

    for obj in selected_objects:
        # Duplicate the object and apply transformations and modifiers
        bpy.ops.object.select_all(action='DESELECT')
        obj.select_set(True)
        bpy.context.view_layer.objects.active = obj
        bpy.ops.object.duplicate()
        bpy.ops.object.transform_apply(location=True, rotation=True, scale=True)
        obj = bpy.context.active_object

        # Create a BMesh to handle triangulation
        bm = bmesh.new()
        bm.from_mesh(obj.data)

        # Triangulate the mesh
        bmesh.ops.triangulate(bm, faces=bm.faces)
        bm.to_mesh(obj.data)
        bm.free()

        mesh = obj.data

        vertices = []
        indices = []

        # Ensure the mesh has tangents and UVs
        if not mesh.uv_layers.active:
            print(f"Mesh {obj.name} has no UV map.")
            bpy.ops.object.delete()
            continue

        mesh.calc_tangents()

        uv_layer = mesh.uv_layers.active.data

        vertex_map = {}
        index = 0

        for poly in mesh.polygons:
            # Ensure consistent face orientation by using polygon normals
            face_normal = poly.normal
            for loop_index in poly.loop_indices:
                loop = mesh.loops[loop_index]
                vert = mesh.vertices[loop.vertex_index]

                vertex = vert.co
                normal = vert.normal.normalized()

                # Flip the normal if it opposes the face normal
                if normal.dot(face_normal) < 0:
                    normal = -normal

                tangent = loop.tangent
                uv = uv_layer[loop.index].uv

                # Key to identify unique vertex data
                key = (
                    vertex.x, vertex.y, vertex.z,
                    normal.x, normal.y, normal.z,
                    tangent.x, tangent.y, tangent.z,
                    uv.x, uv.y
                )

                if key not in vertex_map:
                    vertex_map[key] = index
                    # Store all vertex attributes together in order
                    vertices.append((
                        vertex.x, vertex.y, vertex.z,  # Position
                        normal.x, normal.y, normal.z,  # Normal
                        tangent.x, tangent.y, tangent.z,  # Tangent
                        uv.x, uv.y,  # Texture Coordinates
                        1.0, 1.0, 1.0, 1.0  # Default white vertex color
                    ))
                    index += 1

                indices.append(vertex_map[key])

        # Get the material name (texture) and blend mode
        material_name = obj.active_material.name if obj.active_material else "None"
        blend_mode = obj.active_material.blend_method if obj.active_material else 'OPAQUE'
        is_transparent = blend_mode != 'OPAQUE'

        mesh_data.append((vertices, indices, material_name, is_transparent))

        # Delete the duplicate object
        bpy.ops.object.delete()

    with open(filepath, 'wb') as f:
        # Write number of meshes
        f.write(struct.pack('I', len(mesh_data)))

        for vertices, indices, material_name, is_transparent in mesh_data:
            # Write material name length and material name
            f.write(struct.pack('I', len(material_name)))
            f.write(material_name.encode('utf-8'))

            # Write transparency flag
            f.write(struct.pack('?', is_transparent))

            # Write number of vertices
            f.write(struct.pack('I', len(vertices)))

            # Write all vertex attributes together
            for v in vertices:
                f.write(struct.pack('3f 3f 3f 2f 4f', *v))

            # Write number of indices
            f.write(struct.pack('I', len(indices)))

            # Write indices
            for i in indices:
                f.write(struct.pack('I', i))

    print(f"Meshes exported to {filepath}")

# Properties
def register_properties():
    bpy.types.Scene.search_folder = bpy.props.StringProperty(
        name="Texture Folder",
        description="Folder to search for textures",
        default="",
        subtype='DIR_PATH'
    )
    bpy.types.Scene.export_filepath = bpy.props.StringProperty(
        name="Export Filepath",
        description="Filepath to export meshes",
        default="",
        subtype='FILE_PATH'
    )

def unregister_properties():
    del bpy.types.Scene.search_folder
    del bpy.types.Scene.export_filepath

# Register
classes = [
    MaterialTextureToolPanel,
    CleanMaterialNamesOperator,
    SearchTexturesOperator,
    UpdateTexturesOperator,
    ExportMeshesOperator,
]

def register():
    for cls in classes:
        bpy.utils.register_class(cls)
    register_properties()

def unregister():
    for cls in classes:
        bpy.utils.unregister_class(cls)
    unregister_properties()

if __name__ == "__main__":
    register()