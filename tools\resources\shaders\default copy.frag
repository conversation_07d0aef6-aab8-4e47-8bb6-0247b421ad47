#version 450

in VS_OUT {
    vec3 FragPos;
    vec3 Normal;
    vec2 TexCoords;
    vec4 FragPosLightSpace;
    vec3 fragNormal;
    vec3 fragTangent;
    vec4 fragColor;
} fs_in;

layout (location = 0) out vec4 outColor;

uniform sampler2D albedo;
uniform sampler2D shadowMap; // Blurred shadow map
uniform sampler2D normalMap;
uniform vec4 mainColor;
uniform vec3 ambientLight; // Ambient light color
uniform bool discardTransparent; // Control whether to discard transparent fragments
uniform vec3 lightPos; // Light position
uniform vec3 viewPos; // View position
uniform float shadowBias; // Shadow bias

const float weights[5] = float[](0.227027, 0.1945946, 0.1216216, 0.054054, 0.016216);

float ShadowCalculation(vec4 fragPosLightSpace)
{
    // Perform perspective divide
    vec3 projCoords = fragPosLightSpace.xyz / fragPosLightSpace.w;
    // Transform to [0,1] range
    projCoords = projCoords * 0.5 + 0.5;
    // Get depth of current fragment from light's perspective
    float currentDepth = projCoords.z;

    // Gaussian blur
    float shadow = 0.0;
    vec2 texelSize = 1.0 / textureSize(shadowMap, 0);
    for (int i = -2; i <= 2; ++i)
    {
        for (int j = -2; j <= 2; ++j)
        {
            float weight = weights[abs(i)] * weights[abs(j)];
            float pcfDepth = texture(shadowMap, projCoords.xy + vec2(i, j) * texelSize).r;
            shadow += weight * (currentDepth > pcfDepth + shadowBias ? 1.0 : 0.0);
        }
    }

    return shadow;
}

void main() {

    vec4 texColor = texture(albedo, fs_in.TexCoords);

    // Discard the fragment if the alpha value is below a threshold (e.g., 0.1) and discardTransparent is true
    if (discardTransparent && texColor.a < 0.1) {
        discard;
    }

    // Sample the normal map
    vec3 normalMap = texture(normalMap, fs_in.TexCoords).rgb;
    normalMap = normalMap * 2.0 - 1.0; // Transform from [0,1] range to [-1,1] range

    // Create TBN matrix
    vec3 T = normalize(fs_in.fragTangent);
    vec3 N = normalize(fs_in.fragNormal);
    vec3 B = cross(N, T);
    mat3 TBN = mat3(T, B, N);

    // Transform normal from tangent space to world space
    vec3 normal = normalize(TBN * normalMap);

    // Lighting calculations
    vec3 lightDir = normalize(lightPos - fs_in.FragPos); // Light direction
    vec3 viewDir = normalize(viewPos - fs_in.FragPos); // View direction
    vec3 reflectDir = reflect(-lightDir, normal); // Reflect direction

    // Diffuse shading
    float diff = max(dot(normal, lightDir), 0.0);
    vec3 diffuse = diff * vec3(1.0, 1.0, 1.0); // Diffuse color

    // Specular shading
    float specularStrength = 0.5;
    float shininess = 32.0;
    float spec = pow(max(dot(viewDir, reflectDir), 0.0), shininess);
    vec3 specular = specularStrength * spec * vec3(1.0, 1.0, 1.0); // Specular color

    // Tint the albedo texture with mainColor
    vec3 tintedColor = texColor.rgb * mainColor.rgb;

    // Combine tinted color, vertex color, diffuse lighting, and specular lighting
    vec3 finalColor = tintedColor * fs_in.fragColor.rgb * (diffuse + specular);

    // Add ambient light
    finalColor += ambientLight * tintedColor;

    // Calculate shadow factor
    float shadow = ShadowCalculation(fs_in.FragPosLightSpace);

    // Apply shadow factor
    finalColor = mix(finalColor, finalColor * 0.5, shadow); // Darken the color in shadow

    outColor = vec4(finalColor, texColor.a * fs_in.fragColor.a);
}