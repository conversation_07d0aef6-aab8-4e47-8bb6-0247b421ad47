#pragma once
#include <string>
#include <cstdint>
#include <memory>
#include <fstream>

class Texture
{
public:

    enum class TextureFileType
    {
        DTX,
        DDS,
        PNG,
        JPG,
        BMP,
        TGA
    };

    enum class TextureFormat
    {
        RGB,
        RGBA,
        DXT1,
        DXT3,
        DXT5,
        BGRA,
    };
    
    enum class TextureType
    {
        TEXTURE_2D,
        TEXTURE_NORMAL_MAP,
        TEXTURE_CUBE_MAP,
    };

    Texture();
    ~Texture();

    void SetName(const std::string &name);
    const std::string &GetName() const;

    void SetWidth(int width);
    int GetWidth() const;

    void SetHeight(int height);
    int GetHeight() const;

    void SetChannels(int channels);
    int GetChannels() const;

    void SetFormat(int format);
    int GetFormat() const;

    void SetData(const unsigned char *data, size_t size);
    const unsigned char *GetData() const;

    void Bind(int unit);
    void Unbind();

    void SetTextureFormat(TextureFormat format);
    void SetTextureType(TextureType type);
    void SetStorageType(TextureFileType type);

    void SetTextureID(unsigned int textureID);
    unsigned int GetTextureID() const;

    TextureFormat GetTextureFormat() const;
    TextureType GetTextureType() const;
    TextureFileType GetFileType() const;
    TextureFileType GetFileTypeFromFile(const std::string &fileName) const;

    bool LoadTGA(const std::string &fileName);
    bool LoadPNG(const std::string &fileName);
    bool LoadJPG(const std::string &fileName);
    bool LoadBMP(const std::string &fileName);
    bool LoadDDS(const std::string &fileName);
    bool LoadDTX(const std::string &fileName);
    
    private:

    std::string m_name;
    int m_width;
    int m_height;
    int m_channels;
    unsigned int m_nTextureID;
    unsigned char *m_data;
    size_t m_size;
    TextureFormat m_format;
    TextureType m_type;
    TextureFileType m_storageType;
};