#include "GameTime.h"

// Define static member variables
double Time::deltaTime = 0.0;
double Time::time = 0.0;
double Time::lastTime = 0.0;
float Time::timeScale = 1.0f;
bool Time::isPaused = false;
bool Time::isRunning = true;

Time* g_Time = nullptr; // Initialize the static instance pointer

void Time::Update()
{
    double currentTime = glfwGetTime();
    deltaTime = (currentTime - lastTime) * timeScale; // Calculate delta time
    lastTime = currentTime; // Update last time
    time += deltaTime; // Update total time

    if (isPaused)
    {
        deltaTime = 0.0; // Set delta time to 0 if paused
    }
    if (isRunning)
    {
        time += deltaTime; // Update total time if running
    }
}

float Time::GetDeltaTime() { return Time::deltaTime; }
double Time::GetTime() { return Time::time; }
double Time::GetLastTime() { return Time::lastTime; }
void Time::Reset() { Time::time = 0.0; Time::lastTime = glfwGetTime(); }
void Time::SetTime(double t) { Time::time = t; Time::lastTime = glfwGetTime(); }
void Time::SetDeltaTime(double dt) { Time::deltaTime = dt; }
void Time::SetLastTime(double lt) { Time::lastTime = lt; }
void Time::SetTimeScale(float ts) { Time::timeScale = ts; }
float Time::GetTimeScale() { return Time::timeScale; }
void Time::SetPaused(bool paused) { Time::isPaused = paused; }
bool Time::IsPaused() { return Time::isPaused; }
void Time::SetRunning(bool running) { Time::isRunning = running; }
bool Time::IsRunning() { return Time::isRunning; }