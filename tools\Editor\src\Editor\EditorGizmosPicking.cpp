#include "EditorGizmos.h"
#include "Editor.h"
#include <Components/Light.h>
#include <Components/MeshRenderer.h>
#include <imgui.h>
#include <limits>

namespace Editor {

void EditorGizmos::RenderBillboard(const glm::vec3& position, float size, const glm::mat4& view, 
                                  const glm::mat4& projection, const glm::vec3& color, float alpha, GLuint textureID)
{
    // Get the billboard shader
    auto billboardShader = Renderer::GetShaderManager()->GetShader("billboard");
    if (!billboardShader) return;
    
    // Enable blending for transparency
    glEnable(GL_BLEND);
    glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);

    // Disable depth testing so billboards render in front of all geometry
    //glDisable(GL_DEPTH_TEST);
    glDepthMask(GL_FALSE);
    
    billboardShader->Bind();
    
    // Set uniforms
    billboardShader->SetMat4(view, "uView");
    billboardShader->SetMat4(projection, "uProjection");
    billboardShader->SetVec3(position, "uBillboardPos");
    billboardShader->SetFloat(size, "uBillboardSize");
    billboardShader->SetVec3(color, "uColor");
    billboardShader->SetFloat(alpha, "uAlpha");
    
    // Bind the grid texture
    glActiveTexture(GL_TEXTURE0);
    glBindTexture(GL_TEXTURE_2D, textureID);
    billboardShader->SetInt(0, "uTexture");
    
    // Render the billboard quad
    glBindVertexArray(s_billboardVAO);
    glDrawElements(GL_TRIANGLES, 6, GL_UNSIGNED_INT, 0);
    glBindVertexArray(0);
    
    // Unbind texture
    glBindTexture(GL_TEXTURE_2D, 0);
    
    billboardShader->Unbind();
    
    // Restore depth testing, depth writing, and disable blending
    glEnable(GL_DEPTH_TEST);
    glDepthMask(GL_TRUE);
    glDisable(GL_BLEND);
}

void EditorGizmos::HandleMousePicking(Camera* camera)
{
    // Only handle left mouse clicks (button 0)
    if (!ImGui::IsMouseClicked(0) || !camera) return;

    // Check if mouse is over the scene view
    if (!EditorState::GetInstance().bIsSceneEditorHovered) return;

    // Don't do object picking if we're dragging a gizmo
    if (s_isDragging) return;

    ImVec2 mousePos = ImGui::GetMousePos();

    // Convert mouse position to scene view relative coordinates
    float relativeX = mousePos.x - s_sceneViewX;
    float relativeY = mousePos.y - s_sceneViewY;

    // Check if mouse is within scene view bounds
    if (relativeX < 0 || relativeX > s_sceneViewWidth ||
        relativeY < 0 || relativeY > s_sceneViewHeight) {
        return;
    }

    // Convert to normalized device coordinates relative to scene view
    float x = (2.0f * relativeX) / s_sceneViewWidth - 1.0f;
    float y = 1.0f - (2.0f * relativeY) / s_sceneViewHeight;

    // Calculate view and projection matrices
    glm::vec3 cameraPos = camera->GetGameObject()->GetTransform()->GetPosition();
    glm::vec3 cameraFront = camera->GetGameObject()->GetTransform()->GetForwardVector();
    glm::vec3 cameraUp = camera->GetGameObject()->GetTransform()->GetUpVector();
    glm::mat4 view = glm::lookAt(cameraPos, cameraPos + cameraFront, cameraUp);

    float aspectRatio = s_sceneViewWidth / s_sceneViewHeight;
    glm::mat4 proj = glm::perspective(glm::radians(camera->fov), aspectRatio, camera->nearClip, camera->farClip);

    glm::vec4 ray_clip = glm::vec4(x, y, -1.0f, 1.0f);
    glm::vec4 ray_eye = glm::inverse(proj) * ray_clip;
    ray_eye = glm::vec4(ray_eye.x, ray_eye.y, -1.0f, 0.0f);

    glm::vec3 ray_world = glm::normalize(glm::vec3(glm::inverse(view) * ray_eye));
    glm::vec3 ray_origin = cameraPos;

    auto scene = EditorState::GetInstance().GetActiveScene();
    if (!scene) return;

    // First, check for billboard intersections (since they're always on top)
    auto gameObjects = scene->GetGameObjects();
    float billboardSize = 0.8f; // Same size as rendered billboards
    
    for (size_t i = 0; i < gameObjects.size(); ++i)
    {
        glm::vec3 objPos = gameObjects[i]->GetTransform()->GetWorldPosition();
        
        // Calculate distance from ray to billboard center
        glm::vec3 toObject = objPos - ray_origin;
        float projLength = glm::dot(toObject, ray_world);
        
        if (projLength > 0) // Object is in front of camera
        {
            glm::vec3 closestPoint = ray_origin + ray_world * projLength;
            float distance = glm::length(closestPoint - objPos);
            
            // Check if ray hits the billboard (using a slightly larger hit area for easier selection)
            if (distance <= billboardSize * 0.6f) // 60% of billboard size for hit detection
            {
                SetSelectedGameObjectIndex(static_cast<int>(i));
                s_hasHit = true;
                s_hitPoint = objPos;
                return; // Billboard hit takes priority
            }
        }
    }

    // If no billboard was hit, check for geometry intersections
    float closestT = std::numeric_limits<float>::max();
    glm::vec3 closestHit;
    int hitObjectIndex = -1;

    // Get render queue from Renderer
    auto renderQueue = Renderer::GetRenderQueue();
    for (auto& pair : renderQueue)
    {
        auto& meshRenderer = pair.second;
        Mesh* mesh = meshRenderer->GetMesh().get();
        if (!mesh) continue;

        // Get world transform
        glm::mat4 model = meshRenderer->GetGameObject()->GetTransform()->GetLocalToWorldMatrix();
        
        // Find the index of this GameObject in the scene
        int objectIndex = -1;
        for (size_t i = 0; i < gameObjects.size(); ++i)
        {
            if (gameObjects[i].get() == meshRenderer->GetGameObject())
            {
                objectIndex = static_cast<int>(i);
                break;
            }
        }
        
        if (objectIndex == -1) continue; // GameObject not found in scene

        // Check each submesh
        for (int i = 0; i < mesh->subMeshes.size(); ++i)
        {
            auto& subMesh = mesh->subMeshes[i];
            auto& vertices = subMesh.vertices;
            auto& indices = subMesh.indices;

            for (size_t j = 0; j < indices->size(); j += 3)
            {
                glm::vec3 v0 = model * glm::vec4((*vertices)[(*indices)[j]], 1.0f);
                glm::vec3 v1 = model * glm::vec4((*vertices)[(*indices)[j + 1]], 1.0f);
                glm::vec3 v2 = model * glm::vec4((*vertices)[(*indices)[j + 2]], 1.0f);

                float t;
                if (Raycast3D::RayIntersectsTriangle(ray_origin, ray_world, v0, v1, v2, t))
                {
                    if (t < closestT)
                    {
                        closestT = t;
                        closestHit = ray_origin + ray_world * t;
                        hitObjectIndex = objectIndex;
                    }
                }
            }
        }
    }

    if (hitObjectIndex != -1)
    {
        SetSelectedGameObjectIndex(hitObjectIndex);
        s_hitPoint = closestHit;
        s_hasHit = true;
    }
    else
    {
        // No hit - clear selection
        SetSelectedGameObjectIndex(-1);
        s_hasHit = false;
    }
}

} // namespace Editor
