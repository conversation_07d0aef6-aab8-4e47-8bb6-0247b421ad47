#version 450 core

layout (location = 0) in vec3 position;
layout (location = 1) in vec3 normal;
layout (location = 2) in vec3 tangent;
layout (location = 3) in vec2 texcoord;
layout (location = 4) in vec4 color;

out VS_OUT {
    vec3 worldNormal;
    vec2 texCoords;
} vs_out;

uniform mat4 model;
uniform mat4 view;
uniform mat4 projection;

void main()
{
    // Transform normal to world space
    vs_out.worldNormal = mat3(transpose(inverse(model))) * normal;
    vs_out.texCoords = texcoord;
    
    gl_Position = projection * view * model * vec4(position, 1.0);
}