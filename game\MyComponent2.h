// MyComponent.h
#pragma once
#include "IComponent.h" // Shared interface with the editor
#include <Component.h>
#include "debug.h"

class MyComponent2 : public IComponent, public Component {
public:
    MyComponent2()
    {
        name = "MyComponent2";
    }
    ~MyComponent2() override = default;

    void Start() override;
    void Update() override;
    const char* GetTypeName() const override;
    void SetGameObject(GameObject* obj) override;
    std::string GetField(const std::string& name) const override
    {
        return std::string();
    };
    bool SetField(const std::string& name, const std::string& value) override
    {
        return false;
    };
    std::vector<BuiltInFieldInfo> GetBuiltInFields() override;


    virtual void PrintMembers() override {};
    bool test = true;
    

private:
    
};
